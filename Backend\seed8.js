const mongoose = require("mongoose");
const Reservation = require("./src/models/reservation");
const Hotel = require("./src/models/hotel");
const Room = require("./src/models/room");
const User = require("./src/models/user");
require('dotenv').config();

// Kiểm tra ENVIRONMENT và chọn MongoDB URI phù hợp
const getMongoURI = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  console.log(`🌍 Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log(`📡 Using Production MongoDB: ${process.env.MONGODB_URI_PRODUCTION}`);
    return process.env.MONGODB_URI_PRODUCTION;
  } else {
    console.log(`💻 Using Development MongoDB: ${process.env.MONGODB_URI_DEVELOPMENT}`);
    return process.env.MONGODB_URI_DEVELOPMENT;
  }
};

const mongoURI = getMongoURI();
mongoose.connect(mongoURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log("✅ MongoDB connected successfully");
    console.log(`📍 Connected to: ${mongoURI.includes('mongodb+srv') ? 'MongoDB Atlas (Production)' : 'Local MongoDB (Development)'}`);
})
.catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
});

// Mock data theo bảng dữ liệu 5 tuần gần đây
const weeklyBookingData = [
  // Tuần 4 (4 tuần trước) - Tổng: 10 booking, 1,140,000 VND
  { week: 4, day: 1, bookings: 1, totalAmount: 1200000 },
  { week: 4, day: 2, bookings: 1, totalAmount: 800000 },
  { week: 4, day: 3, bookings: 1, totalAmount: 600000 },
  { week: 4, day: 4, bookings: 1, totalAmount: 500000 },
  { week: 4, day: 5, bookings: 1, totalAmount: 400000 },
  { week: 4, day: 6, bookings: 1, totalAmount: 350000 },
  { week: 4, day: 7, bookings: 1, totalAmount: 350000 },
  { week: 4, day: 8, bookings: 1, totalAmount: 300000 },
  { week: 4, day: 9, bookings: 1, totalAmount: 320000 },
  { week: 4, day: 10, bookings: 1, totalAmount: 320000 },

  // Tuần 5 (3 tuần trước) - Tổng: 15 booking, 6,180,000 VND
  { week: 5, day: 1, bookings: 1, totalAmount: 1100000 },
  { week: 5, day: 2, bookings: 1, totalAmount: 900000 },
  { week: 5, day: 3, bookings: 1, totalAmount: 700000 },
  { week: 5, day: 4, bookings: 1, totalAmount: 500000 },
  { week: 5, day: 5, bookings: 1, totalAmount: 400000 },
  { week: 5, day: 6, bookings: 1, totalAmount: 350000 },
  { week: 5, day: 7, bookings: 1, totalAmount: 350000 },
  { week: 5, day: 8, bookings: 1, totalAmount: 300000 },
  { week: 5, day: 9, bookings: 1, totalAmount: 300000 },
  { week: 5, day: 10, bookings: 1, totalAmount: 300000 },
  { week: 5, day: 11, bookings: 1, totalAmount: 250000 },
  { week: 5, day: 12, bookings: 1, totalAmount: 250000 },
  { week: 5, day: 13, bookings: 1, totalAmount: 250000 },
  { week: 5, day: 14, bookings: 1, totalAmount: 140000 },
  { week: 5, day: 15, bookings: 1, totalAmount: 140000 },

  // Tuần 6 (2 tuần trước) - Tổng: 16 booking, 6,820,000 VND
  { week: 6, day: 1, bookings: 1, totalAmount: 1200000 },
  { week: 6, day: 2, bookings: 1, totalAmount: 900000 },
  { week: 6, day: 3, bookings: 1, totalAmount: 800000 },
  { week: 6, day: 4, bookings: 1, totalAmount: 600000 },
  { week: 6, day: 5, bookings: 1, totalAmount: 500000 },
  { week: 6, day: 6, bookings: 1, totalAmount: 400000 },
  { week: 6, day: 7, bookings: 1, totalAmount: 400000 },
  { week: 6, day: 8, bookings: 1, totalAmount: 300000 },
  { week: 6, day: 9, bookings: 1, totalAmount: 300000 },
  { week: 6, day: 10, bookings: 1, totalAmount: 300000 },
  { week: 6, day: 11, bookings: 1, totalAmount: 250000 },
  { week: 6, day: 12, bookings: 1, totalAmount: 250000 },
  { week: 6, day: 13, bookings: 1, totalAmount: 250000 },
  { week: 6, day: 14, bookings: 1, totalAmount: 200000 },
  { week: 6, day: 15, bookings: 1, totalAmount: 110000 },
  { week: 6, day: 16, bookings: 1, totalAmount: 110000 },

  // Tuần 7 (tuần trước) - Tổng: 13 booking, 6,050,000 VND
  { week: 7, day: 1, bookings: 1, totalAmount: 1000000 },
  { week: 7, day: 2, bookings: 1, totalAmount: 850000 },
  { week: 7, day: 3, bookings: 1, totalAmount: 750000 },
  { week: 7, day: 4, bookings: 1, totalAmount: 700000 },
  { week: 7, day: 5, bookings: 1, totalAmount: 600000 },
  { week: 7, day: 6, bookings: 1, totalAmount: 400000 },
  { week: 7, day: 7, bookings: 1, totalAmount: 350000 },
  { week: 7, day: 8, bookings: 1, totalAmount: 350000 },
  { week: 7, day: 9, bookings: 1, totalAmount: 250000 },
  { week: 7, day: 10, bookings: 1, totalAmount: 250000 },
  { week: 7, day: 11, bookings: 1, totalAmount: 200000 },
  { week: 7, day: 12, bookings: 1, totalAmount: 200000 },
  { week: 7, day: 13, bookings: 1, totalAmount: 200000 },

  // Tuần 8 (tuần hiện tại) - Tổng: 8 booking, 4,090,000 VND
  { week: 8, day: 1, bookings: 1, totalAmount: 1000000 },
  { week: 8, day: 2, bookings: 1, totalAmount: 900000 },
  { week: 8, day: 3, bookings: 1, totalAmount: 600000 },
  { week: 8, day: 4, bookings: 1, totalAmount: 500000 },
  { week: 8, day: 5, bookings: 1, totalAmount: 400000 },
  { week: 8, day: 6, bookings: 1, totalAmount: 300000 },
  { week: 8, day: 7, bookings: 1, totalAmount: 200000 },
  { week: 8, day: 8, bookings: 1, totalAmount: 190000 }
];

// Hàm tạo ngày cho từng tuần
const getDateForWeekAndDay = (weeksAgo, dayInWeek) => {
  const now = new Date();
  const startOfCurrentWeek = new Date(now);
  startOfCurrentWeek.setDate(now.getDate() - now.getDay()); // Chủ nhật đầu tuần
  
  const targetWeekStart = new Date(startOfCurrentWeek);
  targetWeekStart.setDate(startOfCurrentWeek.getDate() - (weeksAgo * 7));
  
  const targetDate = new Date(targetWeekStart);
  targetDate.setDate(targetWeekStart.getDate() + dayInWeek - 1);
  
  // Thêm random giờ trong ngày
  targetDate.setHours(Math.floor(Math.random() * 24));
  targetDate.setMinutes(Math.floor(Math.random() * 60));
  
  return targetDate;
};

// Hàm seed dữ liệu booking
const seedWeeklyBookings = async () => {
  try {
    console.log("🏨 Đang lấy danh sách hotels và rooms...");
    
    // Lấy danh sách hotels và rooms
    const hotels = await Hotel.find({ adminStatus: "APPROVED" }).limit(10);
    const rooms = await Room.find({ statusActive: "ACTIVE" }).limit(20);
    const users = await User.find({ role: "CUSTOMER" }).limit(50);
    
    if (hotels.length === 0 || rooms.length === 0 || users.length === 0) {
      console.log("❌ Không tìm thấy đủ dữ liệu hotels, rooms hoặc users");
      return;
    }
    
    console.log(`✅ Tìm thấy ${hotels.length} hotels, ${rooms.length} rooms, ${users.length} users`);
    
    // Xóa dữ liệu booking cũ của 5 tuần gần đây
    const fiveWeeksAgo = new Date();
    fiveWeeksAgo.setDate(fiveWeeksAgo.getDate() - (5 * 7));
    
    console.log("🗑️  Đang xóa dữ liệu booking cũ của 5 tuần gần đây...");
    const deleteResult = await Reservation.deleteMany({
      createdAt: { $gte: fiveWeeksAgo }
    });
    console.log(`✅ Đã xóa ${deleteResult.deletedCount} booking cũ`);
    
    console.log("📝 Đang tạo mock data booking theo bảng dữ liệu...");
    
    const newBookings = [];
    let totalBookings = 0;
    let totalRevenue = 0;
    
    for (const data of weeklyBookingData) {
      const weeksAgo = 8 - data.week; // Tuần 4 = 4 tuần trước, Tuần 8 = tuần hiện tại
      const bookingDate = getDateForWeekAndDay(weeksAgo, data.day);
      
      // Tạo booking với số tiền theo bảng
      const randomHotel = hotels[Math.floor(Math.random() * hotels.length)];
      const randomRoom = rooms[Math.floor(Math.random() * rooms.length)];
      const randomUser = users[Math.floor(Math.random() * users.length)];
      
      const checkInDate = new Date(bookingDate);
      checkInDate.setDate(checkInDate.getDate() + 1); // Check-in ngày hôm sau
      
      const checkOutDate = new Date(checkInDate);
      checkOutDate.setDate(checkInDate.getDate() + Math.floor(Math.random() * 3) + 1); // 1-3 đêm
      
      const booking = {
        user: randomUser._id,
        hotel: randomHotel._id,
        rooms: [{
          room: randomRoom._id,
          quantity: 1
        }],
        services: [],
        checkInDate,
        checkOutDate,
        status: "COMPLETED",
        totalPrice: data.totalAmount,
        finalPrice: data.totalAmount,
        createdAt: bookingDate,
        updatedAt: bookingDate
      };
      
      newBookings.push(booking);
      totalBookings++;
      totalRevenue += data.totalAmount;
    }
    
    // Insert tất cả booking
    const insertResult = await Reservation.insertMany(newBookings);
    console.log(`✅ Đã tạo ${insertResult.length} booking mới thành công`);
    
    // Thống kê theo tuần
    console.log("\n📊 Thống kê booking theo tuần:");
    const weekStats = {};
    
    weeklyBookingData.forEach(data => {
      if (!weekStats[data.week]) {
        weekStats[data.week] = { bookings: 0, revenue: 0 };
      }
      weekStats[data.week].bookings += data.bookings;
      weekStats[data.week].revenue += data.totalAmount;
    });
    
    Object.keys(weekStats).forEach(week => {
      const stats = weekStats[week];
      console.log(`Tuần ${week}: ${stats.bookings} booking, ${stats.revenue.toLocaleString('vi-VN')} VND`);
    });
    
    console.log(`\n💰 Tổng cộng: ${totalBookings} booking, ${totalRevenue.toLocaleString('vi-VN')} VND`);
    console.log("\n🎉 Seed dữ liệu booking hoàn tất!");
    
    mongoose.disconnect();
  } catch (error) {
    console.error("❌ Lỗi khi seed dữ liệu:", error);
    mongoose.disconnect();
    process.exit(1);
  }
};

// Chạy seed function
seedWeeklyBookings();
