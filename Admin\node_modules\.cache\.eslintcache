[{"E:\\WDP301_UROOM\\Admin\\src\\index.js": "1", "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Admin\\src\\App.js": "3", "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js": "5", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js": "6", "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx": "7", "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx": "8", "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "9", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "10", "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "11", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "12", "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "13", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "14", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "15", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "16", "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "17", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "18", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "19", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "20", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js": "23", "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js": "24", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "26", "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js": "27", "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "28", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js": "29", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js": "30", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js": "31", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js": "32", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js": "33", "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx": "34", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js": "35", "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "36", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "37", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js": "38", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "39", "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js": "40", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js": "41", "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx": "42", "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "43", "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "44", "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js": "45", "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx": "46", "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx": "47", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js": "48", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js": "49", "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js": "50", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js": "51", "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js": "52", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "53", "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js": "54", "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js": "55", "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js": "56", "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js": "57", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js": "58", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js": "59", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js": "60", "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js": "61", "E:\\WDP301_UROOM\\Admin\\src\\utils\\fonts.js": "62", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js": "63", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js": "64", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js": "65", "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js": "66", "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\PromotionUsersModal.jsx": "67"}, {"size": 839, "mtime": 1751249522444, "results": "68", "hashOfConfig": "69"}, {"size": 375, "mtime": 1750045607470, "results": "70", "hashOfConfig": "69"}, {"size": 4119, "mtime": 1751259196611, "results": "71", "hashOfConfig": "69"}, {"size": 1291, "mtime": 1751249522455, "results": "72", "hashOfConfig": "69"}, {"size": 1451, "mtime": 1751249522455, "results": "73", "hashOfConfig": "69"}, {"size": 1302, "mtime": 1751259177977, "results": "74", "hashOfConfig": "69"}, {"size": 1474, "mtime": 1750045607464, "results": "75", "hashOfConfig": "69"}, {"size": 16320, "mtime": 1752935851678, "results": "76", "hashOfConfig": "69"}, {"size": 10290, "mtime": 1751249522447, "results": "77", "hashOfConfig": "69"}, {"size": 15326, "mtime": 1752935851693, "results": "78", "hashOfConfig": "69"}, {"size": 6292, "mtime": 1752935851694, "results": "79", "hashOfConfig": "69"}, {"size": 22581, "mtime": 1752935851681, "results": "80", "hashOfConfig": "69"}, {"size": 14538, "mtime": 1752935851679, "results": "81", "hashOfConfig": "69"}, {"size": 3050, "mtime": 1751249522448, "results": "82", "hashOfConfig": "69"}, {"size": 1723, "mtime": 1751249522448, "results": "83", "hashOfConfig": "69"}, {"size": 18937, "mtime": 1752935851688, "results": "84", "hashOfConfig": "69"}, {"size": 25537, "mtime": 1752935851687, "results": "85", "hashOfConfig": "69"}, {"size": 5423, "mtime": 1751249522449, "results": "86", "hashOfConfig": "69"}, {"size": 3718, "mtime": 1751249522448, "results": "87", "hashOfConfig": "69"}, {"size": 24809, "mtime": 1752935851692, "results": "88", "hashOfConfig": "69"}, {"size": 7403, "mtime": 1751249522449, "results": "89", "hashOfConfig": "69"}, {"size": 7520, "mtime": 1751249522449, "results": "90", "hashOfConfig": "69"}, {"size": 733, "mtime": 1752935851707, "results": "91", "hashOfConfig": "69"}, {"size": 568, "mtime": 1752935851707, "results": "92", "hashOfConfig": "69"}, {"size": 8490, "mtime": 1751249522450, "results": "93", "hashOfConfig": "69"}, {"size": 15641, "mtime": 1751249522449, "results": "94", "hashOfConfig": "69"}, {"size": 551, "mtime": 1751249522456, "results": "95", "hashOfConfig": "69"}, {"size": 24271, "mtime": 1752935851689, "results": "96", "hashOfConfig": "69"}, {"size": 1617, "mtime": 1752935851702, "results": "97", "hashOfConfig": "69"}, {"size": 1424, "mtime": 1752935851700, "results": "98", "hashOfConfig": "69"}, {"size": 196, "mtime": 1751249522452, "results": "99", "hashOfConfig": "69"}, {"size": 466, "mtime": 1751249522454, "results": "100", "hashOfConfig": "69"}, {"size": 764, "mtime": 1751249522452, "results": "101", "hashOfConfig": "69"}, {"size": 2422, "mtime": 1751249522446, "results": "102", "hashOfConfig": "69"}, {"size": 2374, "mtime": 1752935851702, "results": "103", "hashOfConfig": "69"}, {"size": 6543, "mtime": 1751249522446, "results": "104", "hashOfConfig": "69"}, {"size": 35796, "mtime": 1753118185162, "results": "105", "hashOfConfig": "69"}, {"size": 2761, "mtime": 1751249522454, "results": "106", "hashOfConfig": "69"}, {"size": 955, "mtime": 1751249522454, "results": "107", "hashOfConfig": "69"}, {"size": 11749, "mtime": 1752935851705, "results": "108", "hashOfConfig": "69"}, {"size": 1225, "mtime": 1751249522452, "results": "109", "hashOfConfig": "69"}, {"size": 25958, "mtime": 1751249522450, "results": "110", "hashOfConfig": "69"}, {"size": 54984, "mtime": 1753119677148, "results": "111", "hashOfConfig": "69"}, {"size": 2140, "mtime": 1751249522449, "results": "112", "hashOfConfig": "69"}, {"size": 3227, "mtime": 1751249522455, "results": "113", "hashOfConfig": "69"}, {"size": 2348, "mtime": 1750045607431, "results": "114", "hashOfConfig": "69"}, {"size": 1493, "mtime": 1750045607432, "results": "115", "hashOfConfig": "69"}, {"size": 2085, "mtime": 1751249522453, "results": "116", "hashOfConfig": "69"}, {"size": 551, "mtime": 1751249522453, "results": "117", "hashOfConfig": "69"}, {"size": 342, "mtime": 1751249522452, "results": "118", "hashOfConfig": "69"}, {"size": 322, "mtime": 1751249522453, "results": "119", "hashOfConfig": "69"}, {"size": 595, "mtime": 1751249522454, "results": "120", "hashOfConfig": "69"}, {"size": 21444, "mtime": 1753118185160, "results": "121", "hashOfConfig": "69"}, {"size": 377, "mtime": 1751249522453, "results": "122", "hashOfConfig": "69"}, {"size": 4073, "mtime": 1753118185158, "results": "123", "hashOfConfig": "69"}, {"size": 693, "mtime": 1751249522445, "results": "124", "hashOfConfig": "69"}, {"size": 2658, "mtime": 1751249522445, "results": "125", "hashOfConfig": "69"}, {"size": 12311, "mtime": 1753118185167, "results": "126", "hashOfConfig": "69"}, {"size": 6812, "mtime": 1753118185164, "results": "127", "hashOfConfig": "69"}, {"size": 10043, "mtime": 1753118185166, "results": "128", "hashOfConfig": "69"}, {"size": 3543, "mtime": 1753118185166, "results": "129", "hashOfConfig": "69"}, {"size": 636, "mtime": 1751249522456, "results": "130", "hashOfConfig": "69"}, {"size": 1477, "mtime": 1752935851699, "results": "131", "hashOfConfig": "69"}, {"size": 1558, "mtime": 1752935851698, "results": "132", "hashOfConfig": "69"}, {"size": 887, "mtime": 1752935851696, "results": "133", "hashOfConfig": "69"}, {"size": 283, "mtime": 1752935851697, "results": "134", "hashOfConfig": "69"}, {"size": 13987, "mtime": 1753118185162, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tcbxdp", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\WDP301_UROOM\\Admin\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\App.js", ["337"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\BannedPage.jsx", ["338"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\DashboardAdmin.jsx", ["339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["357", "358", "359", "360"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["361", "362"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["363", "364", "365"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["366", "367", "368", "369", "370"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["371", "372", "373", "374"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["375", "376"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["377", "378"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["379", "380", "381", "382"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["383", "384", "385", "386"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["387", "388"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["389", "390", "391", "392", "393"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["394", "395", "396", "397"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["398", "399"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["400", "401", "402", "403", "404"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["405"], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["406", "407"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\SidebarAdmin.jsx", ["408", "409", "410", "411", "412", "413"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["414", "415", "416", "417", "418"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["419", "420"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\messenger\\Chat.jsx", ["421", "422", "423", "424", "425", "426", "427", "428"], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", ["429"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\adapter\\ApiConstants.js", ["430", "431", "432"], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\libs\\api\\index.js", ["433"], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\utils\\fonts.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\saga.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\reducer.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\actions.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\redux\\adminDashboard\\factories.js", [], [], "E:\\WDP301_UROOM\\Admin\\src\\pages\\promotion\\PromotionUsersModal.jsx", ["434", "435", "436", "437", "438", "439", "440"], [], {"ruleId": "441", "severity": 1, "message": "442", "line": 40, "column": 6, "nodeType": "443", "endLine": 40, "endColumn": 17, "suggestions": "444"}, {"ruleId": "445", "severity": 1, "message": "446", "line": 1, "column": 17, "nodeType": "447", "messageId": "448", "endLine": 1, "endColumn": 26}, {"ruleId": "445", "severity": 1, "message": "449", "line": 4, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 14}, {"ruleId": "445", "severity": 1, "message": "450", "line": 4, "column": 16, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "451", "line": 4, "column": 21, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "452", "line": 4, "column": 26, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 34}, {"ruleId": "445", "severity": 1, "message": "453", "line": 21, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 21, "endColumn": 25}, {"ruleId": "445", "severity": 1, "message": "454", "line": 28, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 28, "endColumn": 29}, {"ruleId": "445", "severity": 1, "message": "455", "line": 66, "column": 28, "nodeType": "447", "messageId": "448", "endLine": 66, "endColumn": 47}, {"ruleId": "445", "severity": 1, "message": "456", "line": 68, "column": 25, "nodeType": "447", "messageId": "448", "endLine": 68, "endColumn": 41}, {"ruleId": "457", "severity": 1, "message": "458", "line": 179, "column": 19, "nodeType": "459", "endLine": 179, "endColumn": 76}, {"ruleId": "457", "severity": 1, "message": "458", "line": 189, "column": 19, "nodeType": "459", "endLine": 189, "endColumn": 78}, {"ruleId": "457", "severity": 1, "message": "458", "line": 199, "column": 19, "nodeType": "459", "endLine": 199, "endColumn": 76}, {"ruleId": "457", "severity": 1, "message": "458", "line": 209, "column": 19, "nodeType": "459", "endLine": 209, "endColumn": 77}, {"ruleId": "457", "severity": 1, "message": "458", "line": 219, "column": 19, "nodeType": "459", "endLine": 219, "endColumn": 76}, {"ruleId": "457", "severity": 1, "message": "458", "line": 234, "column": 19, "nodeType": "459", "endLine": 234, "endColumn": 75}, {"ruleId": "457", "severity": 1, "message": "458", "line": 244, "column": 19, "nodeType": "459", "endLine": 244, "endColumn": 84}, {"ruleId": "457", "severity": 1, "message": "458", "line": 254, "column": 19, "nodeType": "459", "endLine": 254, "endColumn": 74}, {"ruleId": "457", "severity": 1, "message": "458", "line": 269, "column": 19, "nodeType": "459", "endLine": 269, "endColumn": 76}, {"ruleId": "457", "severity": 1, "message": "460", "line": 377, "column": 25, "nodeType": "459", "endLine": 392, "endColumn": 26}, {"ruleId": "445", "severity": 1, "message": "461", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "462", "line": 18, "column": 13, "nodeType": "447", "messageId": "448", "endLine": 18, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "463", "line": 23, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 23, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "464", "line": 24, "column": 19, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 29}, {"ruleId": "445", "severity": 1, "message": "465", "line": 8, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 8, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "461", "line": 9, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 9, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "466", "line": 5, "column": 25, "nodeType": "447", "messageId": "448", "endLine": 5, "endColumn": 34}, {"ruleId": "441", "severity": 1, "message": "467", "line": 36, "column": 6, "nodeType": "443", "endLine": 36, "endColumn": 16, "suggestions": "468"}, {"ruleId": "445", "severity": 1, "message": "469", "line": 86, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 86, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "470", "line": 29, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 29, "endColumn": 25}, {"ruleId": "445", "severity": 1, "message": "471", "line": 49, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 49, "endColumn": 21}, {"ruleId": "445", "severity": 1, "message": "472", "line": 52, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 52, "endColumn": 21}, {"ruleId": "445", "severity": 1, "message": "473", "line": 120, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 120, "endColumn": 23}, {"ruleId": "445", "severity": 1, "message": "474", "line": 150, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 150, "endColumn": 27}, {"ruleId": "445", "severity": 1, "message": "446", "line": 1, "column": 20, "nodeType": "447", "messageId": "448", "endLine": 1, "endColumn": 29}, {"ruleId": "445", "severity": 1, "message": "475", "line": 16, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 16}, {"ruleId": "445", "severity": 1, "message": "470", "line": 41, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 41, "endColumn": 25}, {"ruleId": "445", "severity": 1, "message": "474", "line": 50, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 50, "endColumn": 27}, {"ruleId": "445", "severity": 1, "message": "476", "line": 1, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 1, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "461", "line": 1, "column": 29, "nodeType": "447", "messageId": "448", "endLine": 1, "endColumn": 39}, {"ruleId": "445", "severity": 1, "message": "462", "line": 2, "column": 13, "nodeType": "447", "messageId": "448", "endLine": 2, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "463", "line": 6, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "462", "line": 6, "column": 13, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "463", "line": 29, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 29, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "477", "line": 31, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 31, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "473", "line": 116, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 116, "endColumn": 23}, {"ruleId": "445", "severity": 1, "message": "478", "line": 2, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 2, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "479", "line": 22, "column": 29, "nodeType": "447", "messageId": "448", "endLine": 22, "endColumn": 40}, {"ruleId": "445", "severity": 1, "message": "463", "line": 59, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 59, "endColumn": 17}, {"ruleId": "457", "severity": 1, "message": "460", "line": 531, "column": 31, "nodeType": "459", "endLine": 531, "endColumn": 34}, {"ruleId": "445", "severity": 1, "message": "479", "line": 4, "column": 29, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 40}, {"ruleId": "445", "severity": 1, "message": "480", "line": 6, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 6, "endColumn": 15}, {"ruleId": "445", "severity": 1, "message": "479", "line": 4, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 21}, {"ruleId": "445", "severity": 1, "message": "481", "line": 10, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 10, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "482", "line": 12, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 12, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "483", "line": 15, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 15, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "484", "line": 63, "column": 9, "nodeType": "447", "messageId": "448", "endLine": 63, "endColumn": 33}, {"ruleId": "441", "severity": 1, "message": "485", "line": 300, "column": 6, "nodeType": "443", "endLine": 300, "endColumn": 51, "suggestions": "486"}, {"ruleId": "457", "severity": 1, "message": "458", "line": 515, "column": 19, "nodeType": "459", "endLine": 522, "endColumn": 20}, {"ruleId": "457", "severity": 1, "message": "458", "line": 535, "column": 23, "nodeType": "459", "endLine": 542, "endColumn": 24}, {"ruleId": "457", "severity": 1, "message": "458", "line": 550, "column": 19, "nodeType": "459", "endLine": 557, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "479", "line": 4, "column": 29, "nodeType": "447", "messageId": "448", "endLine": 4, "endColumn": 40}, {"ruleId": "457", "severity": 1, "message": "460", "line": 215, "column": 17, "nodeType": "459", "endLine": 219, "endColumn": 52}, {"ruleId": "445", "severity": 1, "message": "479", "line": 3, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 3, "endColumn": 21}, {"ruleId": "445", "severity": 1, "message": "482", "line": 10, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 10, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "487", "line": 22, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 22, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "488", "line": 27, "column": 17, "nodeType": "447", "messageId": "448", "endLine": 27, "endColumn": 25}, {"ruleId": "457", "severity": 1, "message": "458", "line": 203, "column": 17, "nodeType": "459", "endLine": 203, "endColumn": 89}, {"ruleId": "445", "severity": 1, "message": "489", "line": 13, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 13, "endColumn": 19}, {"ruleId": "445", "severity": 1, "message": "482", "line": 30, "column": 8, "nodeType": "447", "messageId": "448", "endLine": 30, "endColumn": 13}, {"ruleId": "441", "severity": 1, "message": "490", "line": 69, "column": 6, "nodeType": "443", "endLine": 69, "endColumn": 8, "suggestions": "491"}, {"ruleId": "457", "severity": 1, "message": "460", "line": 19, "column": 11, "nodeType": "459", "endLine": 24, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "460", "line": 30, "column": 11, "nodeType": "459", "endLine": 35, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "460", "line": 41, "column": 11, "nodeType": "459", "endLine": 46, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "460", "line": 52, "column": 11, "nodeType": "459", "endLine": 57, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "460", "line": 63, "column": 11, "nodeType": "459", "endLine": 68, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "460", "line": 73, "column": 11, "nodeType": "459", "endLine": 78, "endColumn": 12}, {"ruleId": "457", "severity": 1, "message": "458", "line": 171, "column": 15, "nodeType": "459", "endLine": 171, "endColumn": 49}, {"ruleId": "457", "severity": 1, "message": "458", "line": 176, "column": 15, "nodeType": "459", "endLine": 176, "endColumn": 49}, {"ruleId": "457", "severity": 1, "message": "458", "line": 181, "column": 15, "nodeType": "459", "endLine": 181, "endColumn": 49}, {"ruleId": "457", "severity": 1, "message": "458", "line": 186, "column": 15, "nodeType": "459", "endLine": 186, "endColumn": 49}, {"ruleId": "457", "severity": 1, "message": "458", "line": 191, "column": 15, "nodeType": "459", "endLine": 191, "endColumn": 49}, {"ruleId": "445", "severity": 1, "message": "492", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "493", "line": 24, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "494", "line": 10, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 10, "endColumn": 26}, {"ruleId": "445", "severity": 1, "message": "495", "line": 33, "column": 10, "nodeType": "447", "messageId": "448", "endLine": 33, "endColumn": 20}, {"ruleId": "441", "severity": 1, "message": "496", "line": 80, "column": 6, "nodeType": "443", "endLine": 80, "endColumn": 8, "suggestions": "497"}, {"ruleId": "441", "severity": 1, "message": "498", "line": 84, "column": 6, "nodeType": "443", "endLine": 84, "endColumn": 20, "suggestions": "499"}, {"ruleId": "500", "severity": 1, "message": "501", "line": 119, "column": 29, "nodeType": "502", "messageId": "503", "endLine": 119, "endColumn": 31}, {"ruleId": "441", "severity": 1, "message": "496", "line": 142, "column": 6, "nodeType": "443", "endLine": 142, "endColumn": 44, "suggestions": "504"}, {"ruleId": "500", "severity": 1, "message": "501", "line": 417, "column": 46, "nodeType": "502", "messageId": "503", "endLine": 417, "endColumn": 48}, {"ruleId": "500", "severity": 1, "message": "501", "line": 430, "column": 38, "nodeType": "502", "messageId": "503", "endLine": 430, "endColumn": 40}, {"ruleId": "445", "severity": 1, "message": "505", "line": 21, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 21, "endColumn": 9}, {"ruleId": "506", "severity": 1, "message": "507", "line": 10, "column": 3, "nodeType": "508", "messageId": "503", "endLine": 10, "endColumn": 17}, {"ruleId": "506", "severity": 1, "message": "509", "line": 11, "column": 3, "nodeType": "508", "messageId": "503", "endLine": 11, "endColumn": 18}, {"ruleId": "506", "severity": 1, "message": "510", "line": 48, "column": 3, "nodeType": "508", "messageId": "503", "endLine": 48, "endColumn": 28}, {"ruleId": "511", "severity": 1, "message": "512", "line": 37, "column": 1, "nodeType": "513", "endLine": 108, "endColumn": 3}, {"ruleId": "445", "severity": 1, "message": "514", "line": 14, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 14, "endColumn": 7}, {"ruleId": "445", "severity": 1, "message": "492", "line": 16, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 16, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "493", "line": 22, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 22, "endColumn": 11}, {"ruleId": "445", "severity": 1, "message": "515", "line": 24, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 24, "endColumn": 13}, {"ruleId": "445", "severity": 1, "message": "516", "line": 30, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 30, "endColumn": 8}, {"ruleId": "445", "severity": 1, "message": "517", "line": 31, "column": 3, "nodeType": "447", "messageId": "448", "endLine": 31, "endColumn": 10}, {"ruleId": "441", "severity": 1, "message": "518", "line": 60, "column": 6, "nodeType": "443", "endLine": 60, "endColumn": 32, "suggestions": "519"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["520"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'Pagination' is defined but never used.", "'showToast' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadReportedFeedbacks'. Either include it or remove the dependency array.", ["521"], "'getSeverity' is assigned a value but never used.", "'showDeleteModal' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'handleLockCustomer' is assigned a value but never used.", "'useRef' is defined but never used.", "'Col' is defined but never used.", "'loading' is assigned a value but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'Route' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["522"], "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["523"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["524"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["525"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["526"], "'FaEdit' is defined but never used.", "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Card' is defined but never used.", "'FaCalendar' is defined but never used.", "'FaEye' is defined but never used.", "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPromotionUsers'. Either include it or remove the dependency array.", ["527"], {"desc": "528", "fix": "529"}, {"desc": "530", "fix": "531"}, {"desc": "532", "fix": "533"}, {"desc": "534", "fix": "535"}, {"desc": "536", "fix": "537"}, {"desc": "538", "fix": "539"}, {"desc": "540", "fix": "541"}, {"desc": "542", "fix": "543"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "544", "text": "545"}, "Update the dependencies array to be: [dispatch, loadReportedFeedbacks]", {"range": "546", "text": "547"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, selected<PERSON>ear, selectedStatus, fetchPayments]", {"range": "548", "text": "549"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "550", "text": "551"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "552", "text": "553"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "554", "text": "555"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "556", "text": "557"}, "Update the dependencies array to be: [show, promotion, filters, loadPromotionUsers]", {"range": "558", "text": "559"}, [1916, 1927], "[Auth?._id, dispatch]", [1162, 1172], "[dispatch, loadReportedFeedbacks]", [11004, 11049], "[selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, selected<PERSON>tatus, fetchPayments]", [1864, 1866], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [1275, 1301], "[show, promotion, filters, loadPromotionUsers]"]