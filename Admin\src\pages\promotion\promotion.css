/* Enhanced Promotion Management Styles */
.promotion-management {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Type & Visibility Styles */
.type-visibility-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  width: fit-content;
}

.visibility-description {
  font-size: 0.7rem;
  color: #6c757d;
  font-style: italic;
}

/* Status Badge Styles */
.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 6px 10px;
  border-radius: 15px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-width: 90px;
  justify-content: center;
}

.status-active {
  background-color: #28a745 !important;
  color: white !important;
}

.status-upcoming {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.status-expired {
  background-color: #6c757d !important;
  color: white !important;
}

.status-inactive {
  background-color: #dc3545 !important;
  color: white !important;
}

/* Table Row Styling Based on Status */
.promotion-row {
  transition: all 0.2s ease;
}

.promotion-row:hover {
  background-color: #f8f9fa !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Status-based row highlighting */
.promotion-row.status-active {
  border-left: 4px solid #28a745;
}

.promotion-row.status-upcoming {
  border-left: 4px solid #ffc107;
}

.promotion-row.status-expired {
  border-left: 4px solid #6c757d;
  opacity: 0.8;
}

.promotion-row.status-inactive {
  border-left: 4px solid #dc3545;
  opacity: 0.7;
}

/* Enhanced table styling */
.promotions-table {
  border-collapse: separate;
  border-spacing: 0;
}

.promotions-table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 12px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.promotions-table tbody td {
  padding: 15px 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.loading-content {
  text-align: center;
}

.loading-text {
  margin-top: 1rem;
  color: #6c757d;
  font-weight: 500;
}

/* Header Section */
.page-header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.page-title-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.btn-add-promotion {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.btn-add-promotion:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Statistics Cards */
.stats-row {
  margin-top: 2rem;
}

.stat-card {
  border: none;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.stat-card-total .stat-icon {
  background: linear-gradient(45deg, #6f42c1, #e83e8c);
}

.stat-card-active .stat-icon {
  background: linear-gradient(45deg, #28a745, #20c997);
}

.stat-card-upcoming .stat-icon {
  background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.stat-card-inactive .stat-icon {
  background: linear-gradient(45deg, #6c757d, #5a6268);
}

.stat-card-expired .stat-icon {
  background: linear-gradient(45deg, #dc3545, #c82333);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #2c3e50;
}

.stat-label {
  margin: 0;
  color: #6c757d;
  font-weight: 500;
}

/* Content Section */
.content-section {
  padding-bottom: 2rem;
}

.filters-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
}

.search-input .form-control {
  border-left: none;
  padding-left: 0;
}

.search-input .input-group-text {
  background: white;
  border-right: none;
  color: #6c757d;
}

.filter-select {
  border-radius: 10px;
  border: 1px solid #dee2e6;
}

.results-info {
  color: #6c757d;
  font-weight: 500;
}

/* Table Card */
.table-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-wrapper {
  overflow-x: visible;
}

.promotions-table {
  margin: 0;
}

.promotions-table thead th {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: none;
  font-weight: 700;
  color: #495057;
  padding: 1.5rem 1rem;
  position: sticky;
  top: 0;
  z-index: 10;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.promotion-row {
  transition: all 0.3s ease;
  border: none;
}

.promotion-row:hover {
  background: linear-gradient(135deg, #f8f9ff, #fff5f5);
  transform: translateX(5px);
}

.promotion-row td {
  padding: 1.5rem 1rem;
  border: none;
  border-bottom: 1px solid #f1f3f4;
}

/* Promotion Info */
.promotion-info {
  max-width: 300px;
}

.promotion-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.code-badge {
  background: linear-gradient(45deg, #007bff, #6610f2);
  color: white;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: none;
}

.promotion-name {
  margin: 0;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.promotion-description {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Discount Info */
.discount-info {
  text-align: center;
}

.discount-type {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.discount-icon {
  width: 20px;
  height: 20px;
}

.discount-icon.percentage {
  color: #28a745;
}

.discount-icon.fixed {
  color: #007bff;
}

.discount-type-text {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.discount-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.max-discount {
  font-size: 0.8rem;
  color: #6c757d;
}

/* Date Info */
.date-info {
  text-align: center;
}

.date-range {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.date-icon {
  color: #6c757d;
}

.date-separator {
  color: #6c757d;
  font-size: 0.8rem;
  margin: 0.25rem 0;
}

/* Usage Stats */
.usage-stats {
  text-align: center;
  min-width: 120px;
}

.usage-numbers {
  margin-bottom: 0.5rem;
}

.used-count {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.usage-limit {
  color: #6c757d;
  font-weight: 500;
}

.usage-progress-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.usage-progress {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.usage-bar {
  height: 100%;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.usage-percentage {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
  min-width: 35px;
}

/* Status Badges */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Specific status badge styles */
.status-active {
  background: linear-gradient(45deg, #28a745, #20c997) !important;
  color: white !important;
  border: none !important;
}

.status-inactive {
  background: linear-gradient(45deg, #dc3545, #c82333) !important;
  color: white !important;
  border: none !important;
}

.status-expired {
  background: linear-gradient(45deg, #6c757d, #5a6268) !important;
  color: white !important;
  border: none !important;
}

.status-upcoming {
  background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
  color: #212529 !important;
  border: none !important;
}



/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-width: 2px;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.view-btn:hover {
  background: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #ffc107;
  border-color: #ffc107;
  color: white;
}

.toggle-btn:hover {
  background: #6c757d;
  border-color: #6c757d;
  color: white;
}

.delete-btn:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-title {
  color: #6c757d;
  margin-bottom: 1rem;
}

.empty-description {
  color: #adb5bd;
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Pagination */
.pagination-wrapper {
  margin-top: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.pagination .page-link {
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 0.5rem 0.75rem;
  margin: 0 2px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

/* Sortable Headers */
.promotions-table thead th[style*="cursor: pointer"] {
  user-select: none;
  transition: all 0.2s ease;
}

.promotions-table thead th[style*="cursor: pointer"]:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  color: #007bff;
}

/* Loading State */
.loading-state {
  padding: 3rem 0;
}

.loading-state .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Delete Modal */
.delete-modal .modal-content {
  border: none;
  border-radius: 15px;
  overflow: hidden;
}

.delete-modal-header {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
}

.delete-modal-body {
  padding: 2rem;
}

.delete-confirmation {
  text-align: center;
}

.delete-icon {
  width: 80px;
  height: 80px;
  background: #fff5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .stats-row {
    margin-top: 1rem;
  }
  
  .action-buttons {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .action-btn {
    width: 35px;
    height: 35px;
  }
  
  .promotion-info {
    max-width: none;
  }
  
  .usage-stats {
    min-width: auto;
  }
}

@media (max-width: 576px) {
  .page-header-section {
    padding: 1rem 0;
  }
  
  .page-title-wrapper {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .btn-add-promotion {
    width: 100%;
    margin-top: 1rem;
  }
  
  .filters-card .row {
    gap: 1rem;
  }
  
  .pagination-container {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.promotion-row {
  animation: slideInUp 0.3s ease-out;
}

.stat-card {
  animation: slideInUp 0.3s ease-out;
}

.filters-card,
.table-card {
  animation: slideInUp 0.3s ease-out;
}

/* ===== PROMOTION USER MANAGEMENT STYLES ===== */

/* Enhanced Action buttons styling */
.action-buttons {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
  border-width: 1px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.users-btn:hover {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

.stats-btn:hover {
  background-color: #0dcaf0;
  border-color: #0dcaf0;
  color: white;
}

/* Statistics card styling */
.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #adb5bd;
}

.stat-card h4 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.stat-card small {
  color: #6c757d;
  font-weight: 500;
}

/* Status breakdown styling */
.status-breakdown .d-flex {
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.status-breakdown .d-flex:last-child {
  border-bottom: none;
}

.status-breakdown .badge {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.recent-activity .d-flex {
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.recent-activity .d-flex:last-child {
  border-bottom: none;
}

.recent-activity .badge {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

/* Promotion limits info */
.promotion-limits {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.promotion-limits h6 {
  color: #495057;
  margin-bottom: 1rem;
  font-weight: 600;
}

.promotion-limits .row {
  margin-bottom: 0.5rem;
}

.promotion-limits strong {
  color: #495057;
}

/* Users modal styling */
.promotion-users-modal .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.promotion-users-modal .table td {
  vertical-align: middle;
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #f1f3f4;
}

.promotion-users-modal .table tbody tr:hover {
  background-color: #f8f9fa;
}

.promotion-users-modal .user-info {
  display: flex;
  flex-direction: column;
}

.promotion-users-modal .user-info strong {
  color: #495057;
  font-weight: 600;
}

.promotion-users-modal .user-info small {
  color: #6c757d;
  font-size: 0.85rem;
}

/* Progress bar enhancements */
.progress {
  background-color: #e9ecef;
  border-radius: 10px;
  height: 8px;
}

.progress-bar {
  transition: width 0.6s ease;
  border-radius: 10px;
}

/* Badge enhancements */
.badge {
  font-size: 0.75em;
  font-weight: 600;
  padding: 0.35em 0.65em;
}

/* Table responsive improvements */
.table-responsive {
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-responsive .table {
  margin-bottom: 0;
}

.table-responsive .table thead th {
  border-top: none;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f8f9fa;
}

/* Pagination enhancements */
.pagination {
  margin-bottom: 0;
}

.pagination .page-link {
  color: #495057;
  border-color: #dee2e6;
  padding: 0.5rem 0.75rem;
  margin: 0 2px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pagination .page-link:hover {
  color: #0d6efd;
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
  box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

/* Tab styling enhancements */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
  color: #495057;
  border: 1px solid transparent;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
  color: #0d6efd;
}

.nav-tabs .nav-link.active {
  color: #0d6efd;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
  font-weight: 600;
}

/* Loading states */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

/* Responsive adjustments for user management */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: row;
    gap: 0.25rem;
    justify-content: center;
  }

  .action-btn {
    min-width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stat-card {
    margin-bottom: 1rem;
    text-align: center;
  }

  .promotion-users-modal .table {
    font-size: 0.85rem;
  }

  .promotion-users-modal .table th,
  .promotion-users-modal .table td {
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 576px) {
  .action-buttons {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .stat-card h4 {
    font-size: 1.5rem;
  }

  .promotion-limits .row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .nav-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
}