{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Admin\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { Line, Doughnut } from \"react-chartjs-2\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\nimport { FaFilePdf, FaFileExcel } from 'react-icons/fa';\nimport pdfMake from '../../utils/fonts';\nimport { showToast } from '../../components/ToastContainer';\nimport ExcelJS from 'exceljs';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement);\nconst DashboardPage = () => {\n  _s();\n  var _dashboardData$revenu9, _dashboardData$revenu10, _dashboardData$hotelD, _dashboardData$hotelD2, _dashboardData$hotelC, _dashboardData$hotelC2;\n  const dispatch = useDispatch();\n  const {\n    data: dashboardData,\n    loading,\n    error\n  } = useSelector(state => state.AdminDashboard);\n  const [selectedPeriod, setSelectedPeriod] = useState('week');\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n  const [exportLoading, setExportLoading] = useState(false);\n  const [excelExportLoading, setExcelExportLoading] = useState(false);\n\n  // Fetch dashboard data on component mount and when period/year changes\n  useEffect(() => {\n    const params = {\n      period: selectedPeriod\n    };\n    if (selectedPeriod === 'month') {\n      params.year = selectedYear;\n    }\n    dispatch({\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\n      payload: {\n        params,\n        onSuccess: data => {\n          console.log('Dashboard data loaded successfully:', data);\n          // Set selectedYear to first available year if not set\n          if (data.availableYears && data.availableYears.length > 0 && selectedYear === new Date().getFullYear() && !data.availableYears.includes(selectedYear)) {\n            setSelectedYear(data.availableYears[0]);\n          }\n        },\n        onFailed: error => {\n          console.error('Failed to load dashboard data:', error);\n        }\n      }\n    });\n  }, [dispatch, selectedPeriod, selectedYear]);\n\n  // Handle period change\n  const handlePeriodChange = period => {\n    setSelectedPeriod(period);\n    // Reset year to current year when switching periods\n    if (period === 'month') {\n      setSelectedYear(new Date().getFullYear());\n    }\n  };\n\n  // Calculate total revenue from all available data\n  const calculateTotalRevenue = () => {\n    var _dashboardData$revenu, _dashboardData$revenu2, _dashboardData$revenu3;\n    if (!(dashboardData !== null && dashboardData !== void 0 && (_dashboardData$revenu = dashboardData.revenueData) !== null && _dashboardData$revenu !== void 0 && (_dashboardData$revenu2 = _dashboardData$revenu.datasets) !== null && _dashboardData$revenu2 !== void 0 && (_dashboardData$revenu3 = _dashboardData$revenu2[0]) !== null && _dashboardData$revenu3 !== void 0 && _dashboardData$revenu3.data)) {\n      return \"0\";\n    }\n    const totalRevenue = dashboardData.revenueData.datasets[0].data.reduce((sum, value) => sum + (value || 0), 0);\n    return totalRevenue.toLocaleString();\n  };\n\n  // Get revenue breakdown for PDF\n  const getRevenueBreakdown = () => {\n    var _dashboardData$revenu4, _dashboardData$revenu5, _dashboardData$revenu6, _dashboardData$revenu7;\n    if (!(dashboardData !== null && dashboardData !== void 0 && (_dashboardData$revenu4 = dashboardData.revenueData) !== null && _dashboardData$revenu4 !== void 0 && _dashboardData$revenu4.labels) || !(dashboardData !== null && dashboardData !== void 0 && (_dashboardData$revenu5 = dashboardData.revenueData) !== null && _dashboardData$revenu5 !== void 0 && (_dashboardData$revenu6 = _dashboardData$revenu5.datasets) !== null && _dashboardData$revenu6 !== void 0 && (_dashboardData$revenu7 = _dashboardData$revenu6[0]) !== null && _dashboardData$revenu7 !== void 0 && _dashboardData$revenu7.data)) {\n      return [[\"No data available\", \"$0\"]];\n    }\n    const labels = dashboardData.revenueData.labels;\n    const data = dashboardData.revenueData.datasets[0].data;\n    const currentDate = new Date();\n    const currentYear = currentDate.getFullYear();\n    const currentMonth = currentDate.getMonth() + 1; // 1-12\n\n    return labels.map((label, index) => {\n      // Parse year and month from label (e.g., \"Jan 2025\", \"Feb 2025\")\n      const labelParts = label.split(' ');\n      if (labelParts.length === 2) {\n        const labelYear = parseInt(labelParts[1]);\n        const labelMonth = new Date(`${labelParts[0]} 1, ${labelYear}`).getMonth() + 1;\n\n        // Skip future months in current year\n        if (labelYear === currentYear && labelMonth > currentMonth) {\n          return null; // Will be filtered out\n        }\n      }\n      return [label, `$${(data[index] || 0).toLocaleString()}`];\n    }).filter(item => item !== null); // Remove null entries\n  };\n\n  // Get hotel distribution for PDF\n  const getHotelDistribution = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.locationBreakdown)) {\n      return [[\"No data available\", \"0\", \"0\", \"0%\"]];\n    }\n\n    // Calculate total hotels for percentage calculation\n    const totalHotels = dashboardData.locationBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\n    return dashboardData.locationBreakdown.map(item => {\n      const count = item.total || 0;\n      const percentage = totalHotels > 0 ? (count / totalHotels * 100).toFixed(1) : \"0\";\n      return [item.region || \"Unknown\", count.toString(), (item.active || 0).toString(), `${percentage}%`];\n    });\n  };\n\n  // Get hotel classification for PDF\n  const getHotelClassification = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.categoryBreakdown)) {\n      return [[\"No data available\", \"0\", \"0%\"]];\n    }\n\n    // Calculate total hotels for percentage calculation\n    const totalHotels = dashboardData.categoryBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\n    return dashboardData.categoryBreakdown.map(item => {\n      const count = item.total || 0;\n      const percentage = totalHotels > 0 ? (count / totalHotels * 100).toFixed(1) : \"0\";\n      return [item.category || \"Unknown\", count.toString(), `${percentage}%`];\n    });\n  };\n\n  // Export dashboard report as PDF\n  const exportDashboardPDF = () => {\n    if (!dashboardData) {\n      showToast.error(\"No data available for export\");\n      return;\n    }\n    setExportLoading(true);\n    try {\n      const currentDate = new Date().toLocaleDateString('en-US');\n      const periodText = \"COMPREHENSIVE DASHBOARD REPORT\";\n      const docDefinition = {\n        content: [\n        // Header\n        {\n          text: \"UROOM ADMIN DASHBOARD\",\n          style: \"header\",\n          alignment: \"center\",\n          margin: [0, 0, 0, 20]\n        }, {\n          text: periodText.toUpperCase(),\n          style: \"subheader\",\n          alignment: \"center\",\n          margin: [0, 0, 0, 20]\n        }, {\n          text: `Export Date: ${currentDate}`,\n          alignment: \"right\",\n          margin: [0, 0, 0, 30]\n        },\n        // Summary Statistics\n        {\n          text: \"I. OVERVIEW STATISTICS\",\n          style: \"sectionHeader\",\n          margin: [0, 0, 0, 15]\n        }, {\n          table: {\n            widths: [\"50%\", \"50%\"],\n            body: [[\"Total Hotels\", (dashboardData.totalHotels || 0).toString()], [\"Active Hotels\", (dashboardData.activeHotels || 0).toString()], [\"Total Users\", (dashboardData.totalUsers || 0).toString()], [\"Total Hotel Hosts\", (dashboardData.totalOwners || 0).toString()], [\"Total System Revenue\", `$${calculateTotalRevenue()}`]]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Revenue Breakdown\n        {\n          text: \"II. REVENUE BREAKDOWN\",\n          style: \"sectionHeader\",\n          margin: [0, 20, 0, 15]\n        }, {\n          table: {\n            widths: [\"50%\", \"50%\"],\n            body: [[\"Period\", \"Revenue (USD)\"], ...getRevenueBreakdown()]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Hotel Distribution\n        {\n          text: \"III. HOTEL DISTRIBUTION BY REGION\",\n          style: \"sectionHeader\",\n          margin: [0, 20, 0, 15]\n        }, {\n          table: {\n            widths: [\"40%\", \"20%\", \"20%\", \"20%\"],\n            body: [[\"Region\", \"Total\", \"Active\", \"Percentage\"], ...getHotelDistribution()]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Hotel Classification Analysis\n        {\n          text: \"IV. HOTEL CLASSIFICATION ANALYSIS\",\n          style: \"sectionHeader\",\n          margin: [0, 20, 0, 15]\n        }, {\n          table: {\n            widths: [\"50%\", \"25%\", \"25%\"],\n            body: [[\"Classification\", \"Count\", \"Percentage\"], ...getHotelClassification()]\n          },\n          margin: [0, 0, 0, 20]\n        },\n        // Footer\n        {\n          text: \"Report generated automatically by UROOM Admin Dashboard System\",\n          style: \"footer\",\n          alignment: \"center\",\n          margin: [0, 30, 0, 0]\n        }],\n        styles: {\n          header: {\n            fontSize: 20,\n            bold: true,\n            color: \"#212B49\"\n          },\n          subheader: {\n            fontSize: 16,\n            bold: true,\n            color: \"#212B49\"\n          },\n          sectionHeader: {\n            fontSize: 14,\n            bold: true,\n            color: \"#212B49\"\n          },\n          footer: {\n            fontSize: 10,\n            italics: true,\n            color: \"#666666\"\n          }\n        },\n        defaultStyle: {\n          font: \"Roboto\",\n          fallbackFonts: ['Times-Roman']\n        }\n      };\n\n      // Generate PDF\n      pdfMake.createPdf(docDefinition).download(`dashboard-comprehensive-report-${new Date().getTime()}.pdf`);\n      showToast.success(\"PDF report exported successfully!\");\n    } catch (error) {\n      console.error(\"Error exporting dashboard report:\", error);\n      showToast.error(\"Error exporting PDF report: \" + (error.message || \"Unknown error\"));\n    } finally {\n      setExportLoading(false);\n    }\n  };\n\n  // Export dashboard report as Excel with styling\n  const exportDashboardExcel = async () => {\n    if (!dashboardData) {\n      showToast.error(\"No data available for export\");\n      return;\n    }\n    setExcelExportLoading(true);\n    try {\n      const currentDate = new Date().toLocaleDateString('en-US');\n\n      // Create workbook and worksheet\n      const workbook = new ExcelJS.Workbook();\n      const worksheet = workbook.addWorksheet('Dashboard Report');\n\n      // Set column widths\n      worksheet.columns = [{\n        width: 35\n      },\n      // Column A\n      {\n        width: 25\n      },\n      // Column B\n      {\n        width: 18\n      } // Column C\n      ];\n      let currentRow = 1;\n\n      // Main Headers with styling\n      const titleCell = worksheet.getCell('A1');\n      titleCell.value = 'UROOM ADMIN DASHBOARD';\n      titleCell.font = {\n        bold: true,\n        size: 16,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      titleCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF4472C4'\n        }\n      };\n      titleCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      titleCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells('A1:C1');\n      const subtitleCell = worksheet.getCell('A2');\n      subtitleCell.value = 'COMPREHENSIVE DASHBOARD REPORT';\n      subtitleCell.font = {\n        bold: true,\n        size: 14,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      subtitleCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF4472C4'\n        }\n      };\n      subtitleCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      subtitleCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells('A2:C2');\n      const dateCell = worksheet.getCell('A3');\n      dateCell.value = `Export Date: ${currentDate}`;\n      dateCell.font = {\n        bold: true,\n        size: 12,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      dateCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF4472C4'\n        }\n      };\n      dateCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      dateCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells('A3:C3');\n      currentRow = 5;\n\n      // Overview Statistics Section\n      const overviewHeaderCell = worksheet.getCell(`A${currentRow}`);\n      overviewHeaderCell.value = 'OVERVIEW STATISTICS';\n      overviewHeaderCell.font = {\n        bold: true,\n        size: 12,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      overviewHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF70AD47'\n        }\n      };\n      overviewHeaderCell.alignment = {\n        horizontal: 'left',\n        vertical: 'middle'\n      };\n      overviewHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\n      currentRow++;\n\n      // Table headers\n      const metricHeaderCell = worksheet.getCell(`A${currentRow}`);\n      metricHeaderCell.value = 'Metric';\n      metricHeaderCell.font = {\n        bold: true,\n        size: 11\n      };\n      metricHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      metricHeaderCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      metricHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const valueHeaderCell = worksheet.getCell(`B${currentRow}`);\n      valueHeaderCell.value = 'Value';\n      valueHeaderCell.font = {\n        bold: true,\n        size: 11\n      };\n      valueHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      valueHeaderCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      valueHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      currentRow++;\n\n      // Overview data\n      const overviewData = [['Total Hotels', dashboardData.totalHotels || 0], ['Active Hotels', dashboardData.activeHotels || 0], ['Total Users', dashboardData.totalUsers || 0], ['Hotel Owners', dashboardData.totalOwners || 0], ['Total Revenue', `$${(dashboardData.totalRevenue || 0).toLocaleString()}`]];\n      overviewData.forEach(([metric, value]) => {\n        const metricCell = worksheet.getCell(`A${currentRow}`);\n        metricCell.value = metric;\n        metricCell.font = {\n          size: 10\n        };\n        metricCell.alignment = {\n          horizontal: 'left',\n          vertical: 'middle'\n        };\n        metricCell.border = {\n          top: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          bottom: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          left: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          right: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          }\n        };\n        const valueCell = worksheet.getCell(`B${currentRow}`);\n        valueCell.value = value;\n        valueCell.font = {\n          size: 10\n        };\n        valueCell.alignment = {\n          horizontal: 'left',\n          vertical: 'middle'\n        };\n        valueCell.border = {\n          top: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          bottom: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          left: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          right: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          }\n        };\n        currentRow++;\n      });\n      currentRow++; // Empty row\n\n      // Revenue Data Section\n      const revenueHeaderCell = worksheet.getCell(`A${currentRow}`);\n      revenueHeaderCell.value = 'REVENUE DATA';\n      revenueHeaderCell.font = {\n        bold: true,\n        size: 12,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      revenueHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF70AD47'\n        }\n      };\n      revenueHeaderCell.alignment = {\n        horizontal: 'left',\n        vertical: 'middle'\n      };\n      revenueHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\n      currentRow++;\n\n      // Revenue table headers\n      const periodHeaderCell = worksheet.getCell(`A${currentRow}`);\n      periodHeaderCell.value = 'Period';\n      periodHeaderCell.font = {\n        bold: true,\n        size: 11\n      };\n      periodHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      periodHeaderCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      periodHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const revenueValueHeaderCell = worksheet.getCell(`B${currentRow}`);\n      revenueValueHeaderCell.value = 'Revenue (USD)';\n      revenueValueHeaderCell.font = {\n        bold: true,\n        size: 11\n      };\n      revenueValueHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      revenueValueHeaderCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      revenueValueHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      currentRow++;\n\n      // Add revenue data (filter future months)\n      if (dashboardData.revenueData && dashboardData.revenueData.labels) {\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const currentMonth = currentDate.getMonth() + 1;\n        dashboardData.revenueData.labels.forEach((label, index) => {\n          // Parse year and month from label (e.g., \"Jan 2025\", \"Feb 2025\")\n          const labelParts = label.split(' ');\n          let shouldInclude = true;\n          if (labelParts.length === 2) {\n            const labelYear = parseInt(labelParts[1]);\n            const labelMonth = new Date(`${labelParts[0]} 1, ${labelYear}`).getMonth() + 1;\n\n            // Skip future months in current year\n            if (labelYear === currentYear && labelMonth > currentMonth) {\n              shouldInclude = false;\n            }\n          }\n          if (shouldInclude) {\n            var _dashboardData$revenu8;\n            const amount = ((_dashboardData$revenu8 = dashboardData.revenueData.datasets[0]) === null || _dashboardData$revenu8 === void 0 ? void 0 : _dashboardData$revenu8.data[index]) || 0;\n            const periodCell = worksheet.getCell(`A${currentRow}`);\n            periodCell.value = label;\n            periodCell.font = {\n              size: 10\n            };\n            periodCell.alignment = {\n              horizontal: 'left',\n              vertical: 'middle'\n            };\n            periodCell.border = {\n              top: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              bottom: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              left: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              right: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              }\n            };\n            const amountCell = worksheet.getCell(`B${currentRow}`);\n            amountCell.value = `$${amount.toLocaleString()}`;\n            amountCell.font = {\n              size: 10\n            };\n            amountCell.alignment = {\n              horizontal: 'left',\n              vertical: 'middle'\n            };\n            amountCell.border = {\n              top: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              bottom: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              left: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              },\n              right: {\n                style: 'thin',\n                color: {\n                  argb: 'FFD0D0D0'\n                }\n              }\n            };\n            currentRow++;\n          }\n        });\n      }\n      currentRow++; // Empty row\n\n      // Hotel Distribution by Region Section\n      const regionHeaderCell = worksheet.getCell(`A${currentRow}`);\n      regionHeaderCell.value = 'HOTEL DISTRIBUTION BY REGION';\n      regionHeaderCell.font = {\n        bold: true,\n        size: 12,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      regionHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF70AD47'\n        }\n      };\n      regionHeaderCell.alignment = {\n        horizontal: 'left',\n        vertical: 'middle'\n      };\n      regionHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\n      currentRow++;\n\n      // Region table headers\n      const regionLabelCell = worksheet.getCell(`A${currentRow}`);\n      regionLabelCell.value = 'Region';\n      regionLabelCell.font = {\n        bold: true,\n        size: 11\n      };\n      regionLabelCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      regionLabelCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      regionLabelCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const regionCountCell = worksheet.getCell(`B${currentRow}`);\n      regionCountCell.value = 'Count';\n      regionCountCell.font = {\n        bold: true,\n        size: 11\n      };\n      regionCountCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      regionCountCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      regionCountCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const regionPercentCell = worksheet.getCell(`C${currentRow}`);\n      regionPercentCell.value = 'Percentage';\n      regionPercentCell.font = {\n        bold: true,\n        size: 11\n      };\n      regionPercentCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      regionPercentCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      regionPercentCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      currentRow++;\n\n      // Region data\n      if (dashboardData.locationBreakdown && dashboardData.locationBreakdown.length > 0) {\n        const totalHotels = dashboardData.locationBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\n        dashboardData.locationBreakdown.forEach(item => {\n          const count = item.total || 0;\n          const percentage = totalHotels > 0 ? (count / totalHotels * 100).toFixed(1) : \"0\";\n          const regionCell = worksheet.getCell(`A${currentRow}`);\n          regionCell.value = item.region || 'Unknown';\n          regionCell.font = {\n            size: 10\n          };\n          regionCell.alignment = {\n            horizontal: 'left',\n            vertical: 'middle'\n          };\n          regionCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          const countCell = worksheet.getCell(`B${currentRow}`);\n          countCell.value = count;\n          countCell.font = {\n            size: 10\n          };\n          countCell.alignment = {\n            horizontal: 'center',\n            vertical: 'middle'\n          };\n          countCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          const percentCell = worksheet.getCell(`C${currentRow}`);\n          percentCell.value = `${percentage}%`;\n          percentCell.font = {\n            size: 10\n          };\n          percentCell.alignment = {\n            horizontal: 'center',\n            vertical: 'middle'\n          };\n          percentCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          currentRow++;\n        });\n      } else {\n        const noDataCell = worksheet.getCell(`A${currentRow}`);\n        noDataCell.value = 'No data available';\n        noDataCell.font = {\n          size: 10\n        };\n        noDataCell.alignment = {\n          horizontal: 'left',\n          vertical: 'middle'\n        };\n        noDataCell.border = {\n          top: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          bottom: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          left: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          right: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          }\n        };\n        currentRow++;\n      }\n      currentRow++; // Empty row\n\n      // Hotel Classification Section\n      const classificationHeaderCell = worksheet.getCell(`A${currentRow}`);\n      classificationHeaderCell.value = 'HOTEL CLASSIFICATION ANALYSIS';\n      classificationHeaderCell.font = {\n        bold: true,\n        size: 12,\n        color: {\n          argb: 'FFFFFFFF'\n        }\n      };\n      classificationHeaderCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FF70AD47'\n        }\n      };\n      classificationHeaderCell.alignment = {\n        horizontal: 'left',\n        vertical: 'middle'\n      };\n      classificationHeaderCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\n      currentRow++;\n\n      // Classification table headers\n      const classLabelCell = worksheet.getCell(`A${currentRow}`);\n      classLabelCell.value = 'Classification';\n      classLabelCell.font = {\n        bold: true,\n        size: 11\n      };\n      classLabelCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      classLabelCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      classLabelCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const classCountCell = worksheet.getCell(`B${currentRow}`);\n      classCountCell.value = 'Count';\n      classCountCell.font = {\n        bold: true,\n        size: 11\n      };\n      classCountCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      classCountCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      classCountCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      const classPercentCell = worksheet.getCell(`C${currentRow}`);\n      classPercentCell.value = 'Percentage';\n      classPercentCell.font = {\n        bold: true,\n        size: 11\n      };\n      classPercentCell.fill = {\n        type: 'pattern',\n        pattern: 'solid',\n        fgColor: {\n          argb: 'FFE2EFDA'\n        }\n      };\n      classPercentCell.alignment = {\n        horizontal: 'center',\n        vertical: 'middle'\n      };\n      classPercentCell.border = {\n        top: {\n          style: 'thin'\n        },\n        bottom: {\n          style: 'thin'\n        },\n        left: {\n          style: 'thin'\n        },\n        right: {\n          style: 'thin'\n        }\n      };\n      currentRow++;\n\n      // Classification data\n      if (dashboardData.categoryBreakdown && dashboardData.categoryBreakdown.length > 0) {\n        const totalHotels = dashboardData.categoryBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\n        dashboardData.categoryBreakdown.forEach(item => {\n          const count = item.total || 0;\n          const percentage = totalHotels > 0 ? (count / totalHotels * 100).toFixed(1) : \"0\";\n          const classCell = worksheet.getCell(`A${currentRow}`);\n          classCell.value = item.category || 'Unknown';\n          classCell.font = {\n            size: 10\n          };\n          classCell.alignment = {\n            horizontal: 'left',\n            vertical: 'middle'\n          };\n          classCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          const countCell = worksheet.getCell(`B${currentRow}`);\n          countCell.value = count;\n          countCell.font = {\n            size: 10\n          };\n          countCell.alignment = {\n            horizontal: 'center',\n            vertical: 'middle'\n          };\n          countCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          const percentCell = worksheet.getCell(`C${currentRow}`);\n          percentCell.value = `${percentage}%`;\n          percentCell.font = {\n            size: 10\n          };\n          percentCell.alignment = {\n            horizontal: 'center',\n            vertical: 'middle'\n          };\n          percentCell.border = {\n            top: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            bottom: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            left: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            },\n            right: {\n              style: 'thin',\n              color: {\n                argb: 'FFD0D0D0'\n              }\n            }\n          };\n          currentRow++;\n        });\n      } else {\n        const noDataCell = worksheet.getCell(`A${currentRow}`);\n        noDataCell.value = 'No data available';\n        noDataCell.font = {\n          size: 10\n        };\n        noDataCell.alignment = {\n          horizontal: 'left',\n          vertical: 'middle'\n        };\n        noDataCell.border = {\n          top: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          bottom: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          left: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          },\n          right: {\n            style: 'thin',\n            color: {\n              argb: 'FFD0D0D0'\n            }\n          }\n        };\n        currentRow++;\n      }\n\n      // Generate and download file\n      const buffer = await workbook.xlsx.writeBuffer();\n      const blob = new Blob([buffer], {\n        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `dashboard-comprehensive-report-${new Date().getTime()}.xlsx`;\n      a.click();\n      URL.revokeObjectURL(url);\n      showToast.success(\"Excel report exported successfully!\");\n    } catch (error) {\n      console.error(\"Error exporting Excel report:\", error);\n      showToast.error(\"Error exporting Excel report: \" + (error.message || \"Unknown error\"));\n    } finally {\n      setExcelExportLoading(false);\n    }\n  };\n\n  // Chart empty state component\n  const ChartEmptyState = ({\n    icon,\n    message\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex align-items-center justify-content-center h-100 text-muted\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `bi ${icon} fs-1 d-block mb-2`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 761,\n    columnNumber: 5\n  }, this);\n\n  // Format revenue for display (USD)\n  const formatRevenue = revenue => {\n    if (revenue >= 1000000) {\n      return (revenue / 1000000).toFixed(1) + 'M';\n    } else if (revenue >= 1000) {\n      return (revenue / 1000).toFixed(1) + 'K';\n    }\n    return (revenue === null || revenue === void 0 ? void 0 : revenue.toLocaleString()) || '0';\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        style: {\n          height: '400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 782,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"alert-heading\",\n          children: \"Error!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary me-2\",\n          onClick: exportDashboardPDF,\n          disabled: exportLoading || loading,\n          children: exportLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this), \"\\u0110ang xu\\u1EA5t...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), \"Xu\\u1EA5t PDF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-success\",\n          onClick: exportDashboardExcel,\n          disabled: excelExportLoading || loading,\n          children: excelExportLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), \"\\u0110ang xu\\u1EA5t...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaFileExcel, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 17\n            }, this), \"Export Excel\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon hotels\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-building\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.activeHotels || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xE1ch s\\u1EA1n ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon active\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalUsers || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng s\\u1ED1 ng\\u01B0\\u1EDDi d\\xF9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon customers\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-people\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.totalOwners || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ch\\u1EE7 kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon owners\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-person-badge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.websiteVisits || 3034\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"L\\u01B0\\u1EE3t truy c\\u1EADp website\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon visits\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-eye\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"$\", formatRevenue(dashboardData.totalRevenue || 0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"T\\u1ED5ng doanh thu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card-icon revenue\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-currency-dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 854,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Doanh thu h\\u1EC7 th\\u1ED1ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-actions d-flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"btn-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `btn btn-sm ${selectedPeriod === 'week' ? 'btn-primary' : 'btn-outline-secondary'}`,\n              onClick: () => handlePeriodChange('week'),\n              children: \"Tu\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `btn btn-sm ${selectedPeriod === 'month' ? 'btn-primary' : 'btn-outline-secondary'}`,\n              onClick: () => handlePeriodChange('month'),\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `btn btn-sm ${selectedPeriod === 'year' ? 'btn-primary' : 'btn-outline-secondary'}`,\n              onClick: () => handlePeriodChange('year'),\n              children: \"N\\u0103m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this), selectedPeriod === 'month' && (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.availableYears) && /*#__PURE__*/_jsxDEV(\"select\", {\n            className: \"form-select form-select-sm\",\n            value: selectedYear,\n            onChange: e => setSelectedYear(parseInt(e.target.value)),\n            style: {\n              minWidth: '100px'\n            },\n            children: dashboardData.availableYears.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: year,\n              children: year\n            }, year, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-body\",\n        children: ((_dashboardData$revenu9 = dashboardData.revenueData) === null || _dashboardData$revenu9 === void 0 ? void 0 : (_dashboardData$revenu10 = _dashboardData$revenu9.labels) === null || _dashboardData$revenu10 === void 0 ? void 0 : _dashboardData$revenu10.length) > 0 ? /*#__PURE__*/_jsxDEV(Line, {\n          data: dashboardData.revenueData,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                position: \"top\"\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: false,\n                grid: {\n                  drawBorder: false\n                },\n                ticks: {\n                  callback: value => '$' + formatRevenue(value)\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n          icon: \"bi-graph-up\",\n          message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u doanh thu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n b\\u1ED1 kh\\xE1ch s\\u1EA1n theo khu v\\u1EF1c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelD = dashboardData.hotelDistributionData) === null || _dashboardData$hotelD === void 0 ? void 0 : (_dashboardData$hotelD2 = _dashboardData$hotelD.labels) === null || _dashboardData$hotelD2 === void 0 ? void 0 : _dashboardData$hotelD2.length) > 0 ? /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: dashboardData.hotelDistributionData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor = dataset.borderColor) === null || _dataset$borderColor === void 0 ? void 0 : _dataset$borderColor[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              cutout: \"70%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n b\\u1ED1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 996,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container half\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1051,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-body\",\n          children: ((_dashboardData$hotelC = dashboardData.hotelCategoryData) === null || _dashboardData$hotelC === void 0 ? void 0 : (_dashboardData$hotelC2 = _dashboardData$hotelC.labels) === null || _dashboardData$hotelC2 === void 0 ? void 0 : _dashboardData$hotelC2.length) > 0 ? /*#__PURE__*/_jsxDEV(Pie, {\n            data: dashboardData.hotelCategoryData,\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                legend: {\n                  position: \"bottom\",\n                  labels: {\n                    generateLabels: function (chart) {\n                      const data = chart.data;\n                      if (data.labels.length && data.datasets.length) {\n                        const dataset = data.datasets[0];\n                        const total = dataset.data.reduce((sum, value) => sum + value, 0);\n                        return data.labels.map((label, i) => {\n                          var _dataset$borderColor2;\n                          const value = dataset.data[i];\n                          const percentage = (value / total * 100).toFixed(1);\n                          return {\n                            text: `${label}: ${value} (${percentage}%)`,\n                            fillStyle: dataset.backgroundColor[i],\n                            strokeStyle: ((_dataset$borderColor2 = dataset.borderColor) === null || _dataset$borderColor2 === void 0 ? void 0 : _dataset$borderColor2[i]) || '#fff',\n                            lineWidth: 2,\n                            hidden: false,\n                            index: i\n                          };\n                        });\n                      }\n                      return [];\n                    }\n                  }\n                },\n                tooltip: {\n                  callbacks: {\n                    label: function (context) {\n                      const label = context.label || '';\n                      const value = context.parsed;\n                      const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\n                      const percentage = (value / total * 100).toFixed(1);\n                      return `${label}: ${value} khách sạn (${percentage}%)`;\n                    }\n                  }\n                }\n              },\n              onHover: (_, activeElements, chart) => {\n                if (activeElements.length > 0) {\n                  const dataIndex = activeElements[0].index;\n                  const dataset = chart.data.datasets[0];\n                  const total = dataset.data.reduce((sum, val) => sum + val, 0);\n                  const value = dataset.data[dataIndex];\n                  const percentage = (value / total * 100).toFixed(1);\n                  const label = chart.data.labels[dataIndex];\n\n                  // Update center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: `${percentage}%`,\n                    subtext: label,\n                    value: value\n                  };\n                  chart.update('none');\n                } else {\n                  // Reset center text\n                  chart.options.plugins.centerText = {\n                    display: true,\n                    text: 'Click',\n                    subtext: 'để xem chi tiết',\n                    value: ''\n                  };\n                  chart.update('none');\n                }\n              }\n            },\n            plugins: [{\n              id: 'centerTextPie',\n              beforeDraw: chart => {\n                const {\n                  ctx,\n                  width,\n                  height\n                } = chart;\n                const centerText = chart.options.plugins.centerText || {\n                  display: true,\n                  text: 'Click',\n                  subtext: 'để xem chi tiết',\n                  value: ''\n                };\n                if (centerText.display) {\n                  ctx.save();\n                  ctx.textAlign = 'center';\n                  ctx.textBaseline = 'middle';\n                  const centerX = width / 2;\n                  const centerY = height / 2;\n\n                  // Main percentage text\n                  ctx.font = 'bold 20px Arial';\n                  ctx.fillStyle = '#333';\n                  ctx.fillText(centerText.text, centerX, centerY - 8);\n\n                  // Subtext (label)\n                  ctx.font = '12px Arial';\n                  ctx.fillStyle = '#666';\n                  ctx.fillText(centerText.subtext, centerX, centerY + 12);\n\n                  // Value\n                  if (centerText.value) {\n                    ctx.font = '10px Arial';\n                    ctx.fillStyle = '#999';\n                    ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\n                  }\n                  ctx.restore();\n                }\n              }\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(ChartEmptyState, {\n            icon: \"bi-pie-chart-fill\",\n            message: \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n lo\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1169,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1049,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detailed-analysis mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-geo-alt me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch chi ti\\u1EBFt theo khu v\\u1EF1c\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Khu v\\u1EF1c\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Tr\\u1EA1ng th\\xE1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1195,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.locationBreakdown || []).length > 0 ? (dashboardData.locationBreakdown || []).map((location, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: location.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1203,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1202,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: location.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1206,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: location.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1209,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: location.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1212,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1211,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${location.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1217,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1216,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [location.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1222,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1215,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: location.activePercentage >= 80 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1227,\n                      columnNumber: 29\n                    }, this) : location.activePercentage >= 60 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1229,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1231,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1225,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1201,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-geo fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1239,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch khu v\\u1EF1c\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1238,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1237,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-container mb-4 card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"bi bi-star me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 15\n            }, this), \"Ph\\xE2n t\\xEDch theo ph\\xE2n lo\\u1EA1i kh\\xE1ch s\\u1EA1n\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-body card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table table-hover\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ph\\xE2n lo\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1263,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1ED5ng s\\u1ED1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110ang ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EDD ph\\xEA duy\\u1EC7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0110\\xE1nh gi\\xE1 TB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"T\\u1EF7 l\\u1EC7 ho\\u1EA1t \\u0111\\u1ED9ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ch\\u1EA5t l\\u01B0\\u1EE3ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1262,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (dashboardData.categoryBreakdown || []).length > 0 ? (dashboardData.categoryBreakdown || []).map((category, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: category.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1277,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1276,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-primary\",\n                      children: category.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1280,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1279,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: category.active\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1283,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1282,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: category.pending\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1286,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1285,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: `bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`,\n                        style: {\n                          fontSize: '12px'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1291,\n                        columnNumber: 31\n                      }, this)), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"ms-1\",\n                        children: [\"(\", category.avgRating, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1297,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1289,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress me-2\",\n                        style: {\n                          width: '60px',\n                          height: '8px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar bg-success\",\n                          style: {\n                            width: `${category.activePercentage}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1303,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1302,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        children: [category.activePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1308,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1301,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: category.avgRating >= 4.5 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"Xu\\u1EA5t s\\u1EAFc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1313,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 4.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-info\",\n                      children: \"T\\u1ED1t\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1315,\n                      columnNumber: 29\n                    }, this) : category.avgRating >= 3.0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"Trung b\\xECnh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-danger\",\n                      children: \"C\\u1EA7n c\\u1EA3i thi\\u1EC7n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1319,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1311,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center text-muted py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-star fs-1 d-block mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1327,\n                      columnNumber: 25\n                    }, this), \"Ch\\u01B0a c\\xF3 d\\u1EEF li\\u1EC7u ph\\xE2n t\\xEDch ph\\xE2n lo\\u1EA1i\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1326,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 808,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"kvACJQzfogjtfZpU5i7ksPHsMok=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Line", "Doughnut", "useDispatch", "useSelector", "AdminDashboardActions", "FaFilePdf", "FaFileExcel", "pdfMake", "showToast", "ExcelJS", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "DashboardPage", "_s", "_dashboardData$revenu9", "_dashboardData$revenu10", "_dashboardData$hotelD", "_dashboardData$hotelD2", "_dashboardData$hotelC", "_dashboardData$hotelC2", "dispatch", "data", "dashboardData", "loading", "error", "state", "AdminDashboard", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "selected<PERSON>ear", "setSelectedYear", "Date", "getFullYear", "exportLoading", "setExportLoading", "excelExportLoading", "setExcelExportLoading", "params", "period", "year", "type", "FETCH_ADMIN_DASHBOARD_METRICS", "payload", "onSuccess", "console", "log", "availableYears", "length", "includes", "onFailed", "handlePeriodChange", "calculateTotalRevenue", "_dashboardData$revenu", "_dashboardData$revenu2", "_dashboardData$revenu3", "revenueData", "datasets", "totalRevenue", "reduce", "sum", "value", "toLocaleString", "getRevenueBreakdown", "_dashboardData$revenu4", "_dashboardData$revenu5", "_dashboardData$revenu6", "_dashboardData$revenu7", "labels", "currentDate", "currentYear", "currentMonth", "getMonth", "map", "label", "index", "labelParts", "split", "labelYear", "parseInt", "labelMonth", "filter", "item", "getHotelDistribution", "locationBreakdown", "totalHotels", "total", "count", "percentage", "toFixed", "region", "toString", "active", "getHotelClassification", "categoryBreakdown", "category", "exportDashboardPDF", "toLocaleDateString", "periodText", "docDefinition", "content", "text", "style", "alignment", "margin", "toUpperCase", "table", "widths", "body", "activeHotels", "totalUsers", "totalOwners", "styles", "header", "fontSize", "bold", "color", "subheader", "section<PERSON><PERSON><PERSON>", "footer", "italics", "defaultStyle", "font", "fallbackFonts", "createPdf", "download", "getTime", "success", "message", "exportDashboardExcel", "workbook", "Workbook", "worksheet", "addWorksheet", "columns", "width", "currentRow", "title<PERSON>ell", "getCell", "size", "argb", "fill", "pattern", "fgColor", "horizontal", "vertical", "border", "top", "bottom", "left", "right", "mergeCells", "subtitleCell", "dateCell", "overviewHeaderCell", "metricHeaderCell", "valueHeaderCell", "overviewData", "for<PERSON>ach", "metric", "metricCell", "valueCell", "revenueHeaderCell", "periodHeaderCell", "revenueValueHeaderCell", "shouldInclude", "_dashboardData$revenu8", "amount", "periodCell", "amountCell", "regionHeaderCell", "regionLabelCell", "regionCountCell", "regionPercentCell", "regionCell", "<PERSON><PERSON><PERSON>", "percentCell", "noDataCell", "classificationHeaderCell", "classLabelCell", "classCountCell", "classPercentCell", "classCell", "buffer", "xlsx", "writeBuffer", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "click", "revokeObjectURL", "ChartEmptyState", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatRevenue", "revenue", "height", "role", "onClick", "disabled", "websiteVisits", "onChange", "e", "target", "min<PERSON><PERSON><PERSON>", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "scales", "y", "beginAtZero", "grid", "drawBorder", "ticks", "callback", "x", "display", "hotelDistributionData", "generateLabels", "chart", "dataset", "i", "_dataset$borderColor", "fillStyle", "backgroundColor", "strokeStyle", "borderColor", "lineWidth", "hidden", "tooltip", "callbacks", "context", "parsed", "val", "cutout", "hotelCategoryData", "Pie", "_dataset$borderColor2", "onHover", "_", "activeElements", "dataIndex", "centerText", "subtext", "update", "id", "beforeDraw", "ctx", "save", "textAlign", "textBaseline", "centerX", "centerY", "fillText", "restore", "location", "pending", "activePercentage", "colSpan", "Array", "Math", "floor", "avgRating", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Admin/src/pages/dashboard/DashboardPage.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { Line, Doughnut } from \"react-chartjs-2\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport AdminDashboardActions from \"../../redux/adminDashboard/actions\";\r\nimport { FaFilePdf, FaFileExcel } from 'react-icons/fa';\r\nimport pdfMake from '../../utils/fonts';\r\nimport { showToast } from '../../components/ToastContainer';\r\nimport ExcelJS from 'exceljs';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement,\r\n} from 'chart.js';\r\n\r\n// Register Chart.js components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  ArcElement\r\n);\r\n\r\nconst DashboardPage = () => {\r\n  const dispatch = useDispatch();\r\n  const { data: dashboardData, loading, error } = useSelector(state => state.AdminDashboard);\r\n  const [selectedPeriod, setSelectedPeriod] = useState('week');\r\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const [excelExportLoading, setExcelExportLoading] = useState(false);\r\n\r\n\r\n\r\n  // Fetch dashboard data on component mount and when period/year changes\r\n  useEffect(() => {\r\n    const params = { period: selectedPeriod };\r\n    if (selectedPeriod === 'month') {\r\n      params.year = selectedYear;\r\n    }\r\n\r\n    dispatch({\r\n      type: AdminDashboardActions.FETCH_ADMIN_DASHBOARD_METRICS,\r\n      payload: {\r\n        params,\r\n        onSuccess: (data) => {\r\n          console.log('Dashboard data loaded successfully:', data);\r\n          // Set selectedYear to first available year if not set\r\n          if (data.availableYears && data.availableYears.length > 0 && selectedYear === new Date().getFullYear() && !data.availableYears.includes(selectedYear)) {\r\n            setSelectedYear(data.availableYears[0]);\r\n          }\r\n        },\r\n        onFailed: (error) => {\r\n          console.error('Failed to load dashboard data:', error);\r\n        }\r\n      }\r\n    });\r\n  }, [dispatch, selectedPeriod, selectedYear]);\r\n\r\n  // Handle period change\r\n  const handlePeriodChange = (period) => {\r\n    setSelectedPeriod(period);\r\n    // Reset year to current year when switching periods\r\n    if (period === 'month') {\r\n      setSelectedYear(new Date().getFullYear());\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Calculate total revenue from all available data\r\n  const calculateTotalRevenue = () => {\r\n    if (!dashboardData?.revenueData?.datasets?.[0]?.data) {\r\n      return \"0\";\r\n    }\r\n    const totalRevenue = dashboardData.revenueData.datasets[0].data.reduce((sum, value) => sum + (value || 0), 0);\r\n    return totalRevenue.toLocaleString();\r\n  };\r\n\r\n  // Get revenue breakdown for PDF\r\n  const getRevenueBreakdown = () => {\r\n    if (!dashboardData?.revenueData?.labels || !dashboardData?.revenueData?.datasets?.[0]?.data) {\r\n      return [[\"No data available\", \"$0\"]];\r\n    }\r\n\r\n    const labels = dashboardData.revenueData.labels;\r\n    const data = dashboardData.revenueData.datasets[0].data;\r\n    const currentDate = new Date();\r\n    const currentYear = currentDate.getFullYear();\r\n    const currentMonth = currentDate.getMonth() + 1; // 1-12\r\n\r\n    return labels.map((label, index) => {\r\n      // Parse year and month from label (e.g., \"Jan 2025\", \"Feb 2025\")\r\n      const labelParts = label.split(' ');\r\n      if (labelParts.length === 2) {\r\n        const labelYear = parseInt(labelParts[1]);\r\n        const labelMonth = new Date(`${labelParts[0]} 1, ${labelYear}`).getMonth() + 1;\r\n\r\n        // Skip future months in current year\r\n        if (labelYear === currentYear && labelMonth > currentMonth) {\r\n          return null; // Will be filtered out\r\n        }\r\n      }\r\n\r\n      return [\r\n        label,\r\n        `$${(data[index] || 0).toLocaleString()}`\r\n      ];\r\n    }).filter(item => item !== null); // Remove null entries\r\n  };\r\n\r\n  // Get hotel distribution for PDF\r\n  const getHotelDistribution = () => {\r\n    if (!dashboardData?.locationBreakdown) {\r\n      return [[\"No data available\", \"0\", \"0\", \"0%\"]];\r\n    }\r\n\r\n    // Calculate total hotels for percentage calculation\r\n    const totalHotels = dashboardData.locationBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\r\n\r\n    return dashboardData.locationBreakdown.map(item => {\r\n      const count = item.total || 0;\r\n      const percentage = totalHotels > 0 ? ((count / totalHotels) * 100).toFixed(1) : \"0\";\r\n\r\n      return [\r\n        item.region || \"Unknown\",\r\n        count.toString(),\r\n        (item.active || 0).toString(),\r\n        `${percentage}%`\r\n      ];\r\n    });\r\n  };\r\n\r\n  // Get hotel classification for PDF\r\n  const getHotelClassification = () => {\r\n    if (!dashboardData?.categoryBreakdown) {\r\n      return [[\"No data available\", \"0\", \"0%\"]];\r\n    }\r\n\r\n    // Calculate total hotels for percentage calculation\r\n    const totalHotels = dashboardData.categoryBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\r\n\r\n    return dashboardData.categoryBreakdown.map(item => {\r\n      const count = item.total || 0;\r\n      const percentage = totalHotels > 0 ? ((count / totalHotels) * 100).toFixed(1) : \"0\";\r\n\r\n      return [\r\n        item.category || \"Unknown\",\r\n        count.toString(),\r\n        `${percentage}%`\r\n      ];\r\n    });\r\n  };\r\n\r\n  // Export dashboard report as PDF\r\n  const exportDashboardPDF = () => {\r\n    if (!dashboardData) {\r\n      showToast.error(\"No data available for export\");\r\n      return;\r\n    }\r\n\r\n    setExportLoading(true);\r\n\r\n    try {\r\n      const currentDate = new Date().toLocaleDateString('en-US');\r\n      const periodText = \"COMPREHENSIVE DASHBOARD REPORT\";\r\n\r\n\r\n\r\n      const docDefinition = {\r\n        content: [\r\n          // Header\r\n          {\r\n            text: \"UROOM ADMIN DASHBOARD\",\r\n            style: \"header\",\r\n            alignment: \"center\",\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n          {\r\n            text: periodText.toUpperCase(),\r\n            style: \"subheader\",\r\n            alignment: \"center\",\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n          {\r\n            text: `Export Date: ${currentDate}`,\r\n            alignment: \"right\",\r\n            margin: [0, 0, 0, 30],\r\n          },\r\n\r\n          // Summary Statistics\r\n          {\r\n            text: \"I. OVERVIEW STATISTICS\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 0, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"50%\", \"50%\"],\r\n              body: [\r\n                [\"Total Hotels\", (dashboardData.totalHotels || 0).toString()],\r\n                [\"Active Hotels\", (dashboardData.activeHotels || 0).toString()],\r\n                [\"Total Users\", (dashboardData.totalUsers || 0).toString()],\r\n                [\"Total Hotel Hosts\", (dashboardData.totalOwners || 0).toString()],\r\n                [\"Total System Revenue\", `$${calculateTotalRevenue()}`],\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Revenue Breakdown\r\n          {\r\n            text: \"II. REVENUE BREAKDOWN\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 20, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"50%\", \"50%\"],\r\n              body: [\r\n                [\"Period\", \"Revenue (USD)\"],\r\n                ...getRevenueBreakdown()\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Hotel Distribution\r\n          {\r\n            text: \"III. HOTEL DISTRIBUTION BY REGION\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 20, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"40%\", \"20%\", \"20%\", \"20%\"],\r\n              body: [\r\n                [\"Region\", \"Total\", \"Active\", \"Percentage\"],\r\n                ...getHotelDistribution()\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Hotel Classification Analysis\r\n          {\r\n            text: \"IV. HOTEL CLASSIFICATION ANALYSIS\",\r\n            style: \"sectionHeader\",\r\n            margin: [0, 20, 0, 15],\r\n          },\r\n          {\r\n            table: {\r\n              widths: [\"50%\", \"25%\", \"25%\"],\r\n              body: [\r\n                [\"Classification\", \"Count\", \"Percentage\"],\r\n                ...getHotelClassification()\r\n              ],\r\n            },\r\n            margin: [0, 0, 0, 20],\r\n          },\r\n\r\n          // Footer\r\n          {\r\n            text: \"Report generated automatically by UROOM Admin Dashboard System\",\r\n            style: \"footer\",\r\n            alignment: \"center\",\r\n            margin: [0, 30, 0, 0],\r\n          },\r\n        ],\r\n        styles: {\r\n          header: {\r\n            fontSize: 20,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          subheader: {\r\n            fontSize: 16,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          sectionHeader: {\r\n            fontSize: 14,\r\n            bold: true,\r\n            color: \"#212B49\",\r\n          },\r\n          footer: {\r\n            fontSize: 10,\r\n            italics: true,\r\n            color: \"#666666\",\r\n          },\r\n        },\r\n        defaultStyle: {\r\n          font: \"Roboto\",\r\n          fallbackFonts: ['Times-Roman']\r\n        },\r\n      };\r\n\r\n      // Generate PDF\r\n      pdfMake.createPdf(docDefinition).download(`dashboard-comprehensive-report-${new Date().getTime()}.pdf`);\r\n      showToast.success(\"PDF report exported successfully!\");\r\n    } catch (error) {\r\n      console.error(\"Error exporting dashboard report:\", error);\r\n      showToast.error(\"Error exporting PDF report: \" + (error.message || \"Unknown error\"));\r\n    } finally {\r\n      setExportLoading(false);\r\n    }\r\n  };\r\n\r\n  // Export dashboard report as Excel with styling\r\n  const exportDashboardExcel = async () => {\r\n    if (!dashboardData) {\r\n      showToast.error(\"No data available for export\");\r\n      return;\r\n    }\r\n\r\n    setExcelExportLoading(true);\r\n\r\n    try {\r\n      const currentDate = new Date().toLocaleDateString('en-US');\r\n\r\n      // Create workbook and worksheet\r\n      const workbook = new ExcelJS.Workbook();\r\n      const worksheet = workbook.addWorksheet('Dashboard Report');\r\n\r\n      // Set column widths\r\n      worksheet.columns = [\r\n        { width: 35 }, // Column A\r\n        { width: 25 }, // Column B\r\n        { width: 18 }  // Column C\r\n      ];\r\n\r\n      let currentRow = 1;\r\n\r\n      // Main Headers with styling\r\n      const titleCell = worksheet.getCell('A1');\r\n      titleCell.value = 'UROOM ADMIN DASHBOARD';\r\n      titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };\r\n      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };\r\n      titleCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      titleCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells('A1:C1');\r\n\r\n      const subtitleCell = worksheet.getCell('A2');\r\n      subtitleCell.value = 'COMPREHENSIVE DASHBOARD REPORT';\r\n      subtitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };\r\n      subtitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };\r\n      subtitleCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      subtitleCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells('A2:C2');\r\n\r\n      const dateCell = worksheet.getCell('A3');\r\n      dateCell.value = `Export Date: ${currentDate}`;\r\n      dateCell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };\r\n      dateCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };\r\n      dateCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      dateCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells('A3:C3');\r\n\r\n      currentRow = 5;\r\n\r\n      // Overview Statistics Section\r\n      const overviewHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      overviewHeaderCell.value = 'OVERVIEW STATISTICS';\r\n      overviewHeaderCell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };\r\n      overviewHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF70AD47' } };\r\n      overviewHeaderCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n      overviewHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\r\n      currentRow++;\r\n\r\n      // Table headers\r\n      const metricHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      metricHeaderCell.value = 'Metric';\r\n      metricHeaderCell.font = { bold: true, size: 11 };\r\n      metricHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      metricHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      metricHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const valueHeaderCell = worksheet.getCell(`B${currentRow}`);\r\n      valueHeaderCell.value = 'Value';\r\n      valueHeaderCell.font = { bold: true, size: 11 };\r\n      valueHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      valueHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      valueHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      currentRow++;\r\n\r\n      // Overview data\r\n      const overviewData = [\r\n        ['Total Hotels', dashboardData.totalHotels || 0],\r\n        ['Active Hotels', dashboardData.activeHotels || 0],\r\n        ['Total Users', dashboardData.totalUsers || 0],\r\n        ['Hotel Owners', dashboardData.totalOwners || 0],\r\n        ['Total Revenue', `$${(dashboardData.totalRevenue || 0).toLocaleString()}`]\r\n      ];\r\n\r\n      overviewData.forEach(([metric, value]) => {\r\n        const metricCell = worksheet.getCell(`A${currentRow}`);\r\n        metricCell.value = metric;\r\n        metricCell.font = { size: 10 };\r\n        metricCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n        metricCell.border = {\r\n          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n        };\r\n\r\n        const valueCell = worksheet.getCell(`B${currentRow}`);\r\n        valueCell.value = value;\r\n        valueCell.font = { size: 10 };\r\n        valueCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n        valueCell.border = {\r\n          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n        };\r\n        currentRow++;\r\n      });\r\n\r\n      currentRow++; // Empty row\r\n\r\n      // Revenue Data Section\r\n      const revenueHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      revenueHeaderCell.value = 'REVENUE DATA';\r\n      revenueHeaderCell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };\r\n      revenueHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF70AD47' } };\r\n      revenueHeaderCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n      revenueHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\r\n      currentRow++;\r\n\r\n      // Revenue table headers\r\n      const periodHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      periodHeaderCell.value = 'Period';\r\n      periodHeaderCell.font = { bold: true, size: 11 };\r\n      periodHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      periodHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      periodHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const revenueValueHeaderCell = worksheet.getCell(`B${currentRow}`);\r\n      revenueValueHeaderCell.value = 'Revenue (USD)';\r\n      revenueValueHeaderCell.font = { bold: true, size: 11 };\r\n      revenueValueHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      revenueValueHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      revenueValueHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      currentRow++;\r\n\r\n      // Add revenue data (filter future months)\r\n      if (dashboardData.revenueData && dashboardData.revenueData.labels) {\r\n        const currentDate = new Date();\r\n        const currentYear = currentDate.getFullYear();\r\n        const currentMonth = currentDate.getMonth() + 1;\r\n\r\n        dashboardData.revenueData.labels.forEach((label, index) => {\r\n          // Parse year and month from label (e.g., \"Jan 2025\", \"Feb 2025\")\r\n          const labelParts = label.split(' ');\r\n          let shouldInclude = true;\r\n\r\n          if (labelParts.length === 2) {\r\n            const labelYear = parseInt(labelParts[1]);\r\n            const labelMonth = new Date(`${labelParts[0]} 1, ${labelYear}`).getMonth() + 1;\r\n\r\n            // Skip future months in current year\r\n            if (labelYear === currentYear && labelMonth > currentMonth) {\r\n              shouldInclude = false;\r\n            }\r\n          }\r\n\r\n          if (shouldInclude) {\r\n            const amount = dashboardData.revenueData.datasets[0]?.data[index] || 0;\r\n\r\n            const periodCell = worksheet.getCell(`A${currentRow}`);\r\n            periodCell.value = label;\r\n            periodCell.font = { size: 10 };\r\n            periodCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n            periodCell.border = {\r\n              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n            };\r\n\r\n            const amountCell = worksheet.getCell(`B${currentRow}`);\r\n            amountCell.value = `$${amount.toLocaleString()}`;\r\n            amountCell.font = { size: 10 };\r\n            amountCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n            amountCell.border = {\r\n              top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n              right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n            };\r\n            currentRow++;\r\n          }\r\n        });\r\n      }\r\n\r\n      currentRow++; // Empty row\r\n\r\n      // Hotel Distribution by Region Section\r\n      const regionHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      regionHeaderCell.value = 'HOTEL DISTRIBUTION BY REGION';\r\n      regionHeaderCell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };\r\n      regionHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF70AD47' } };\r\n      regionHeaderCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n      regionHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\r\n      currentRow++;\r\n\r\n      // Region table headers\r\n      const regionLabelCell = worksheet.getCell(`A${currentRow}`);\r\n      regionLabelCell.value = 'Region';\r\n      regionLabelCell.font = { bold: true, size: 11 };\r\n      regionLabelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      regionLabelCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      regionLabelCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const regionCountCell = worksheet.getCell(`B${currentRow}`);\r\n      regionCountCell.value = 'Count';\r\n      regionCountCell.font = { bold: true, size: 11 };\r\n      regionCountCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      regionCountCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      regionCountCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const regionPercentCell = worksheet.getCell(`C${currentRow}`);\r\n      regionPercentCell.value = 'Percentage';\r\n      regionPercentCell.font = { bold: true, size: 11 };\r\n      regionPercentCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      regionPercentCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      regionPercentCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      currentRow++;\r\n\r\n      // Region data\r\n      if (dashboardData.locationBreakdown && dashboardData.locationBreakdown.length > 0) {\r\n        const totalHotels = dashboardData.locationBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\r\n\r\n        dashboardData.locationBreakdown.forEach(item => {\r\n          const count = item.total || 0;\r\n          const percentage = totalHotels > 0 ? ((count / totalHotels) * 100).toFixed(1) : \"0\";\r\n\r\n          const regionCell = worksheet.getCell(`A${currentRow}`);\r\n          regionCell.value = item.region || 'Unknown';\r\n          regionCell.font = { size: 10 };\r\n          regionCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n          regionCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n\r\n          const countCell = worksheet.getCell(`B${currentRow}`);\r\n          countCell.value = count;\r\n          countCell.font = { size: 10 };\r\n          countCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n          countCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n\r\n          const percentCell = worksheet.getCell(`C${currentRow}`);\r\n          percentCell.value = `${percentage}%`;\r\n          percentCell.font = { size: 10 };\r\n          percentCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n          percentCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n          currentRow++;\r\n        });\r\n      } else {\r\n        const noDataCell = worksheet.getCell(`A${currentRow}`);\r\n        noDataCell.value = 'No data available';\r\n        noDataCell.font = { size: 10 };\r\n        noDataCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n        noDataCell.border = {\r\n          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n        };\r\n        currentRow++;\r\n      }\r\n\r\n      currentRow++; // Empty row\r\n\r\n      // Hotel Classification Section\r\n      const classificationHeaderCell = worksheet.getCell(`A${currentRow}`);\r\n      classificationHeaderCell.value = 'HOTEL CLASSIFICATION ANALYSIS';\r\n      classificationHeaderCell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };\r\n      classificationHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF70AD47' } };\r\n      classificationHeaderCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n      classificationHeaderCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      worksheet.mergeCells(`A${currentRow}:C${currentRow}`);\r\n      currentRow++;\r\n\r\n      // Classification table headers\r\n      const classLabelCell = worksheet.getCell(`A${currentRow}`);\r\n      classLabelCell.value = 'Classification';\r\n      classLabelCell.font = { bold: true, size: 11 };\r\n      classLabelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      classLabelCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      classLabelCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const classCountCell = worksheet.getCell(`B${currentRow}`);\r\n      classCountCell.value = 'Count';\r\n      classCountCell.font = { bold: true, size: 11 };\r\n      classCountCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      classCountCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      classCountCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n\r\n      const classPercentCell = worksheet.getCell(`C${currentRow}`);\r\n      classPercentCell.value = 'Percentage';\r\n      classPercentCell.font = { bold: true, size: 11 };\r\n      classPercentCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE2EFDA' } };\r\n      classPercentCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n      classPercentCell.border = {\r\n        top: { style: 'thin' }, bottom: { style: 'thin' },\r\n        left: { style: 'thin' }, right: { style: 'thin' }\r\n      };\r\n      currentRow++;\r\n\r\n      // Classification data\r\n      if (dashboardData.categoryBreakdown && dashboardData.categoryBreakdown.length > 0) {\r\n        const totalHotels = dashboardData.categoryBreakdown.reduce((sum, item) => sum + (item.total || 0), 0);\r\n\r\n        dashboardData.categoryBreakdown.forEach(item => {\r\n          const count = item.total || 0;\r\n          const percentage = totalHotels > 0 ? ((count / totalHotels) * 100).toFixed(1) : \"0\";\r\n\r\n          const classCell = worksheet.getCell(`A${currentRow}`);\r\n          classCell.value = item.category || 'Unknown';\r\n          classCell.font = { size: 10 };\r\n          classCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n          classCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n\r\n          const countCell = worksheet.getCell(`B${currentRow}`);\r\n          countCell.value = count;\r\n          countCell.font = { size: 10 };\r\n          countCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n          countCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n\r\n          const percentCell = worksheet.getCell(`C${currentRow}`);\r\n          percentCell.value = `${percentage}%`;\r\n          percentCell.font = { size: 10 };\r\n          percentCell.alignment = { horizontal: 'center', vertical: 'middle' };\r\n          percentCell.border = {\r\n            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n            right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n          };\r\n          currentRow++;\r\n        });\r\n      } else {\r\n        const noDataCell = worksheet.getCell(`A${currentRow}`);\r\n        noDataCell.value = 'No data available';\r\n        noDataCell.font = { size: 10 };\r\n        noDataCell.alignment = { horizontal: 'left', vertical: 'middle' };\r\n        noDataCell.border = {\r\n          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },\r\n          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }\r\n        };\r\n        currentRow++;\r\n      }\r\n\r\n      // Generate and download file\r\n      const buffer = await workbook.xlsx.writeBuffer();\r\n      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n      const url = URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `dashboard-comprehensive-report-${new Date().getTime()}.xlsx`;\r\n      a.click();\r\n      URL.revokeObjectURL(url);\r\n\r\n      showToast.success(\"Excel report exported successfully!\");\r\n    } catch (error) {\r\n      console.error(\"Error exporting Excel report:\", error);\r\n      showToast.error(\"Error exporting Excel report: \" + (error.message || \"Unknown error\"));\r\n    } finally {\r\n      setExcelExportLoading(false);\r\n    }\r\n  };\r\n\r\n  // Chart empty state component\r\n  const ChartEmptyState = ({ icon, message }) => (\r\n    <div className=\"d-flex align-items-center justify-content-center h-100 text-muted\">\r\n      <div className=\"text-center\">\r\n        <i className={`bi ${icon} fs-1 d-block mb-2`}></i>\r\n        <p>{message}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Format revenue for display (USD)\r\n  const formatRevenue = (revenue) => {\r\n    if (revenue >= 1000000) {\r\n      return (revenue / 1000000).toFixed(1) + 'M';\r\n    } else if (revenue >= 1000) {\r\n      return (revenue / 1000).toFixed(1) + 'K';\r\n    }\r\n    return revenue?.toLocaleString() || '0';\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-content\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          <h4 className=\"alert-heading\">Error!</h4>\r\n          <p>{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"dashboard-content\">\r\n      <div className=\"page-header\">\r\n        <h1>Tổng quan hệ thống</h1>\r\n        <div className=\"page-actions\">\r\n          <button\r\n            className=\"btn btn-primary me-2\"\r\n            onClick={exportDashboardPDF}\r\n            disabled={exportLoading || loading}\r\n          >\r\n            {exportLoading ? (\r\n              <>\r\n                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n                Đang xuất...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFilePdf className=\"me-2\" />\r\n                Xuất PDF\r\n              </>\r\n            )}\r\n          </button>\r\n          <button\r\n            className=\"btn btn-success\"\r\n            onClick={exportDashboardExcel}\r\n            disabled={excelExportLoading || loading}\r\n          >\r\n            {excelExportLoading ? (\r\n              <>\r\n                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n                Đang xuất...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFileExcel className=\"me-2\" />\r\n                Export Excel\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"stats-cards\">\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalHotels || 0}</h3>\r\n            <p>Tổng số khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon hotels\">\r\n            <i className=\"bi bi-building\"></i>\r\n          </div>\r\n        </div>\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.activeHotels || 0}</h3>\r\n            <p>Khách sạn hoạt động</p>\r\n          </div>\r\n          <div className=\"stat-card-icon active\">\r\n            <i className=\"bi bi-check-circle\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalUsers || 0}</h3>\r\n            <p>Tổng số người dùng</p>\r\n          </div>\r\n          <div className=\"stat-card-icon customers\">\r\n            <i className=\"bi bi-people\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.totalOwners || 0}</h3>\r\n            <p>Chủ khách sạn</p>\r\n          </div>\r\n          <div className=\"stat-card-icon owners\">\r\n            <i className=\"bi bi-person-badge\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>{dashboardData.websiteVisits || 3034}</h3>\r\n            <p>Lượt truy cập website</p>\r\n          </div>\r\n          <div className=\"stat-card-icon visits\">\r\n            <i className=\"bi bi-eye\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"stat-card\">\r\n          <div className=\"stat-card-content\">\r\n            <h3>${formatRevenue(dashboardData.totalRevenue || 0)}</h3>\r\n            <p>Tổng doanh thu</p>\r\n          </div>\r\n          <div className=\"stat-card-icon revenue\">\r\n            <i className=\"bi bi-currency-dollar\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Revenue Chart */}\r\n      <div className=\"chart-container\">\r\n        <div className=\"chart-header\">\r\n          <h2>Doanh thu hệ thống</h2>\r\n          <div className=\"chart-actions d-flex gap-2\">\r\n            <div className=\"btn-group\">\r\n              <button\r\n                className={`btn btn-sm ${selectedPeriod === 'week' ? 'btn-primary' : 'btn-outline-secondary'}`}\r\n                onClick={() => handlePeriodChange('week')}\r\n              >\r\n                Tuần\r\n              </button>\r\n              <button\r\n                className={`btn btn-sm ${selectedPeriod === 'month' ? 'btn-primary' : 'btn-outline-secondary'}`}\r\n                onClick={() => handlePeriodChange('month')}\r\n              >\r\n                Tháng\r\n              </button>\r\n              <button\r\n                className={`btn btn-sm ${selectedPeriod === 'year' ? 'btn-primary' : 'btn-outline-secondary'}`}\r\n                onClick={() => handlePeriodChange('year')}\r\n              >\r\n                Năm\r\n              </button>\r\n            </div>\r\n            {selectedPeriod === 'month' && dashboardData?.availableYears && (\r\n              <select\r\n                className=\"form-select form-select-sm\"\r\n                value={selectedYear}\r\n                onChange={(e) => setSelectedYear(parseInt(e.target.value))}\r\n                style={{ minWidth: '100px' }}\r\n              >\r\n                {dashboardData.availableYears.map(year => (\r\n                  <option key={year} value={year}>{year}</option>\r\n                ))}\r\n              </select>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-body\">\r\n          {dashboardData.revenueData?.labels?.length > 0 ? (\r\n            <Line\r\n              data={dashboardData.revenueData}\r\n              options={{\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                plugins: {\r\n                  legend: {\r\n                    position: \"top\",\r\n                  },\r\n                },\r\n                scales: {\r\n                  y: {\r\n                    beginAtZero: false,\r\n                    grid: {\r\n                      drawBorder: false,\r\n                    },\r\n                    ticks: {\r\n                      callback: (value) => '$' + formatRevenue(value),\r\n                    },\r\n                  },\r\n                  x: {\r\n                    grid: {\r\n                      display: false,\r\n                    },\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          ) : (\r\n            <ChartEmptyState icon=\"bi-graph-up\" message=\"Chưa có dữ liệu doanh thu\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Distribution Charts */}\r\n      <div className=\"charts-row\">\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân bố khách sạn theo khu vực</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelDistributionData?.labels?.length > 0 ? (\r\n              <Doughnut\r\n                data={dashboardData.hotelDistributionData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  cutout: \"70%\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart\" message=\"Chưa có dữ liệu phân bố\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"chart-container half\">\r\n          <div className=\"chart-header\">\r\n            <h2>Phân loại khách sạn</h2>\r\n          </div>\r\n          <div className=\"chart-body\">\r\n            {dashboardData.hotelCategoryData?.labels?.length > 0 ? (\r\n              <Pie\r\n                data={dashboardData.hotelCategoryData}\r\n                options={{\r\n                  responsive: true,\r\n                  maintainAspectRatio: false,\r\n                  plugins: {\r\n                    legend: {\r\n                      position: \"bottom\",\r\n                      labels: {\r\n                        generateLabels: function(chart) {\r\n                          const data = chart.data;\r\n                          if (data.labels.length && data.datasets.length) {\r\n                            const dataset = data.datasets[0];\r\n                            const total = dataset.data.reduce((sum, value) => sum + value, 0);\r\n                            return data.labels.map((label, i) => {\r\n                              const value = dataset.data[i];\r\n                              const percentage = ((value / total) * 100).toFixed(1);\r\n                              return {\r\n                                text: `${label}: ${value} (${percentage}%)`,\r\n                                fillStyle: dataset.backgroundColor[i],\r\n                                strokeStyle: dataset.borderColor?.[i] || '#fff',\r\n                                lineWidth: 2,\r\n                                hidden: false,\r\n                                index: i\r\n                              };\r\n                            });\r\n                          }\r\n                          return [];\r\n                        }\r\n                      }\r\n                    },\r\n                    tooltip: {\r\n                      callbacks: {\r\n                        label: function(context) {\r\n                          const label = context.label || '';\r\n                          const value = context.parsed;\r\n                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);\r\n                          const percentage = ((value / total) * 100).toFixed(1);\r\n                          return `${label}: ${value} khách sạn (${percentage}%)`;\r\n                        }\r\n                      }\r\n                    }\r\n                  },\r\n                  onHover: (_, activeElements, chart) => {\r\n                    if (activeElements.length > 0) {\r\n                      const dataIndex = activeElements[0].index;\r\n                      const dataset = chart.data.datasets[0];\r\n                      const total = dataset.data.reduce((sum, val) => sum + val, 0);\r\n                      const value = dataset.data[dataIndex];\r\n                      const percentage = ((value / total) * 100).toFixed(1);\r\n                      const label = chart.data.labels[dataIndex];\r\n\r\n                      // Update center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: `${percentage}%`,\r\n                        subtext: label,\r\n                        value: value\r\n                      };\r\n                      chart.update('none');\r\n                    } else {\r\n                      // Reset center text\r\n                      chart.options.plugins.centerText = {\r\n                        display: true,\r\n                        text: 'Click',\r\n                        subtext: 'để xem chi tiết',\r\n                        value: ''\r\n                      };\r\n                      chart.update('none');\r\n                    }\r\n                  },\r\n                }}\r\n                plugins={[{\r\n                  id: 'centerTextPie',\r\n                  beforeDraw: (chart) => {\r\n                    const { ctx, width, height } = chart;\r\n                    const centerText = chart.options.plugins.centerText || {\r\n                      display: true,\r\n                      text: 'Click',\r\n                      subtext: 'để xem chi tiết',\r\n                      value: ''\r\n                    };\r\n\r\n                    if (centerText.display) {\r\n                      ctx.save();\r\n                      ctx.textAlign = 'center';\r\n                      ctx.textBaseline = 'middle';\r\n\r\n                      const centerX = width / 2;\r\n                      const centerY = height / 2;\r\n\r\n                      // Main percentage text\r\n                      ctx.font = 'bold 20px Arial';\r\n                      ctx.fillStyle = '#333';\r\n                      ctx.fillText(centerText.text, centerX, centerY - 8);\r\n\r\n                      // Subtext (label)\r\n                      ctx.font = '12px Arial';\r\n                      ctx.fillStyle = '#666';\r\n                      ctx.fillText(centerText.subtext, centerX, centerY + 12);\r\n\r\n                      // Value\r\n                      if (centerText.value) {\r\n                        ctx.font = '10px Arial';\r\n                        ctx.fillStyle = '#999';\r\n                        ctx.fillText(`${centerText.value} khách sạn`, centerX, centerY + 28);\r\n                      }\r\n\r\n                      ctx.restore();\r\n                    }\r\n                  }\r\n                }]}\r\n              />\r\n            ) : (\r\n              <ChartEmptyState icon=\"bi-pie-chart-fill\" message=\"Chưa có dữ liệu phân loại\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detailed Analysis */}\r\n      <div className=\"detailed-analysis mt-4\">\r\n        {/* Location Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-geo-alt me-2\"></i>\r\n              Phân tích chi tiết theo khu vực\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Khu vực</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Trạng thái</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.locationBreakdown || []).length > 0 ? (\r\n                    (dashboardData.locationBreakdown || []).map((location, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{location.region}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{location.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{location.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{location.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${location.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{location.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {location.activePercentage >= 80 ? (\r\n                            <span className=\"badge bg-success\">Tốt</span>\r\n                          ) : location.activePercentage >= 60 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"6\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-geo fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích khu vực\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Breakdown */}\r\n        <div className=\"analysis-container mb-4 card\">\r\n          <div className=\"analysis-header d-flex justify-content-between align-items-center p-3 border-bottom\">\r\n            <h2 className=\"mb-0\">\r\n              <i className=\"bi bi-star me-2\"></i>\r\n              Phân tích theo phân loại khách sạn\r\n            </h2>\r\n          </div>\r\n          <div className=\"analysis-body card-body\">\r\n            <div className=\"table-responsive\">\r\n              <table className=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Phân loại</th>\r\n                    <th>Tổng số</th>\r\n                    <th>Đang hoạt động</th>\r\n                    <th>Chờ phê duyệt</th>\r\n                    <th>Đánh giá TB</th>\r\n                    <th>Tỷ lệ hoạt động</th>\r\n                    <th>Chất lượng</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {(dashboardData.categoryBreakdown || []).length > 0 ? (\r\n                    (dashboardData.categoryBreakdown || []).map((category, index) => (\r\n                      <tr key={index}>\r\n                        <td>\r\n                          <strong>{category.category}</strong>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-primary\">{category.total}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-success\">{category.active}</span>\r\n                        </td>\r\n                        <td>\r\n                          <span className=\"badge bg-warning\">{category.pending}</span>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            {[...Array(5)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className={`bi ${i < Math.floor(category.avgRating || 0) ? 'bi-star-fill' : 'bi-star'} text-warning me-1`}\r\n                                style={{ fontSize: '12px' }}\r\n                              ></i>\r\n                            ))}\r\n                            <small className=\"ms-1\">({category.avgRating})</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div className=\"progress me-2\" style={{ width: '60px', height: '8px' }}>\r\n                              <div\r\n                                className=\"progress-bar bg-success\"\r\n                                style={{ width: `${category.activePercentage}%` }}\r\n                              ></div>\r\n                            </div>\r\n                            <small>{category.activePercentage}%</small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          {category.avgRating >= 4.5 ? (\r\n                            <span className=\"badge bg-success\">Xuất sắc</span>\r\n                          ) : category.avgRating >= 4.0 ? (\r\n                            <span className=\"badge bg-info\">Tốt</span>\r\n                          ) : category.avgRating >= 3.0 ? (\r\n                            <span className=\"badge bg-warning\">Trung bình</span>\r\n                          ) : (\r\n                            <span className=\"badge bg-danger\">Cần cải thiện</span>\r\n                          )}\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  ) : (\r\n                    <tr>\r\n                      <td colSpan=\"7\" className=\"text-center text-muted py-4\">\r\n                        <i className=\"bi bi-star fs-1 d-block mb-2\"></i>\r\n                        Chưa có dữ liệu phân tích phân loại\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,QAAQ,QAAQ,iBAAiB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,SAASC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AACvD,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,OAAOC,OAAO,MAAM,SAAS;AAC7B,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAZ,OAAO,CAACa,QAAQ,CACdZ,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,uBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgC,IAAI,EAAEC,aAAa;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGlC,WAAW,CAACmC,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;EAC1F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI8C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAInE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmD,MAAM,GAAG;MAAEC,MAAM,EAAEX;IAAe,CAAC;IACzC,IAAIA,cAAc,KAAK,OAAO,EAAE;MAC9BU,MAAM,CAACE,IAAI,GAAGV,YAAY;IAC5B;IAEAT,QAAQ,CAAC;MACPoB,IAAI,EAAEjD,qBAAqB,CAACkD,6BAA6B;MACzDC,OAAO,EAAE;QACPL,MAAM;QACNM,SAAS,EAAGtB,IAAI,IAAK;UACnBuB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAExB,IAAI,CAAC;UACxD;UACA,IAAIA,IAAI,CAACyB,cAAc,IAAIzB,IAAI,CAACyB,cAAc,CAACC,MAAM,GAAG,CAAC,IAAIlB,YAAY,KAAK,IAAIE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI,CAACX,IAAI,CAACyB,cAAc,CAACE,QAAQ,CAACnB,YAAY,CAAC,EAAE;YACrJC,eAAe,CAACT,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC,CAAC;UACzC;QACF,CAAC;QACDG,QAAQ,EAAGzB,KAAK,IAAK;UACnBoB,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,QAAQ,EAAEO,cAAc,EAAEE,YAAY,CAAC,CAAC;;EAE5C;EACA,MAAMqB,kBAAkB,GAAIZ,MAAM,IAAK;IACrCV,iBAAiB,CAACU,MAAM,CAAC;IACzB;IACA,IAAIA,MAAM,KAAK,OAAO,EAAE;MACtBR,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;;EAID;EACA,MAAMmB,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAClC,IAAI,EAAChC,aAAa,aAAbA,aAAa,gBAAA8B,qBAAA,GAAb9B,aAAa,CAAEiC,WAAW,cAAAH,qBAAA,gBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BI,QAAQ,cAAAH,sBAAA,gBAAAC,sBAAA,GAApCD,sBAAA,CAAuC,CAAC,CAAC,cAAAC,sBAAA,eAAzCA,sBAAA,CAA2CjC,IAAI,GAAE;MACpD,OAAO,GAAG;IACZ;IACA,MAAMoC,YAAY,GAAGnC,aAAa,CAACiC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACnC,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,OAAOH,YAAY,CAACI,cAAc,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChC,IAAI,EAAC5C,aAAa,aAAbA,aAAa,gBAAAyC,sBAAA,GAAbzC,aAAa,CAAEiC,WAAW,cAAAQ,sBAAA,eAA1BA,sBAAA,CAA4BI,MAAM,KAAI,EAAC7C,aAAa,aAAbA,aAAa,gBAAA0C,sBAAA,GAAb1C,aAAa,CAAEiC,WAAW,cAAAS,sBAAA,gBAAAC,sBAAA,GAA1BD,sBAAA,CAA4BR,QAAQ,cAAAS,sBAAA,gBAAAC,sBAAA,GAApCD,sBAAA,CAAuC,CAAC,CAAC,cAAAC,sBAAA,eAAzCA,sBAAA,CAA2C7C,IAAI,GAAE;MAC3F,OAAO,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACtC;IAEA,MAAM8C,MAAM,GAAG7C,aAAa,CAACiC,WAAW,CAACY,MAAM;IAC/C,MAAM9C,IAAI,GAAGC,aAAa,CAACiC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACnC,IAAI;IACvD,MAAM+C,WAAW,GAAG,IAAIrC,IAAI,CAAC,CAAC;IAC9B,MAAMsC,WAAW,GAAGD,WAAW,CAACpC,WAAW,CAAC,CAAC;IAC7C,MAAMsC,YAAY,GAAGF,WAAW,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEjD,OAAOJ,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAClC;MACA,MAAMC,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;MACnC,IAAID,UAAU,CAAC5B,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM8B,SAAS,GAAGC,QAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;QACzC,MAAMI,UAAU,GAAG,IAAIhD,IAAI,CAAC,GAAG4C,UAAU,CAAC,CAAC,CAAC,OAAOE,SAAS,EAAE,CAAC,CAACN,QAAQ,CAAC,CAAC,GAAG,CAAC;;QAE9E;QACA,IAAIM,SAAS,KAAKR,WAAW,IAAIU,UAAU,GAAGT,YAAY,EAAE;UAC1D,OAAO,IAAI,CAAC,CAAC;QACf;MACF;MAEA,OAAO,CACLG,KAAK,EACL,IAAI,CAACpD,IAAI,CAACqD,KAAK,CAAC,IAAI,CAAC,EAAEb,cAAc,CAAC,CAAC,EAAE,CAC1C;IACH,CAAC,CAAC,CAACmB,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,EAAC5D,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAE6D,iBAAiB,GAAE;MACrC,OAAO,CAAC,CAAC,mBAAmB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAChD;;IAEA;IACA,MAAMC,WAAW,GAAG9D,aAAa,CAAC6D,iBAAiB,CAACzB,MAAM,CAAC,CAACC,GAAG,EAAEsB,IAAI,KAAKtB,GAAG,IAAIsB,IAAI,CAACI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAErG,OAAO/D,aAAa,CAAC6D,iBAAiB,CAACX,GAAG,CAACS,IAAI,IAAI;MACjD,MAAMK,KAAK,GAAGL,IAAI,CAACI,KAAK,IAAI,CAAC;MAC7B,MAAME,UAAU,GAAGH,WAAW,GAAG,CAAC,GAAG,CAAEE,KAAK,GAAGF,WAAW,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;MAEnF,OAAO,CACLP,IAAI,CAACQ,MAAM,IAAI,SAAS,EACxBH,KAAK,CAACI,QAAQ,CAAC,CAAC,EAChB,CAACT,IAAI,CAACU,MAAM,IAAI,CAAC,EAAED,QAAQ,CAAC,CAAC,EAC7B,GAAGH,UAAU,GAAG,CACjB;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACtE,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEuE,iBAAiB,GAAE;MACrC,OAAO,CAAC,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC3C;;IAEA;IACA,MAAMT,WAAW,GAAG9D,aAAa,CAACuE,iBAAiB,CAACnC,MAAM,CAAC,CAACC,GAAG,EAAEsB,IAAI,KAAKtB,GAAG,IAAIsB,IAAI,CAACI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAErG,OAAO/D,aAAa,CAACuE,iBAAiB,CAACrB,GAAG,CAACS,IAAI,IAAI;MACjD,MAAMK,KAAK,GAAGL,IAAI,CAACI,KAAK,IAAI,CAAC;MAC7B,MAAME,UAAU,GAAGH,WAAW,GAAG,CAAC,GAAG,CAAEE,KAAK,GAAGF,WAAW,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;MAEnF,OAAO,CACLP,IAAI,CAACa,QAAQ,IAAI,SAAS,EAC1BR,KAAK,CAACI,QAAQ,CAAC,CAAC,EAChB,GAAGH,UAAU,GAAG,CACjB;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACzE,aAAa,EAAE;MAClB3B,SAAS,CAAC6B,KAAK,CAAC,8BAA8B,CAAC;MAC/C;IACF;IAEAU,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMkC,WAAW,GAAG,IAAIrC,IAAI,CAAC,CAAC,CAACiE,kBAAkB,CAAC,OAAO,CAAC;MAC1D,MAAMC,UAAU,GAAG,gCAAgC;MAInD,MAAMC,aAAa,GAAG;QACpBC,OAAO,EAAE;QACP;QACA;UACEC,IAAI,EAAE,uBAAuB;UAC7BC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEH,IAAI,EAAEH,UAAU,CAACO,WAAW,CAAC,CAAC;UAC9BH,KAAK,EAAE,WAAW;UAClBC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEH,IAAI,EAAE,gBAAgBhC,WAAW,EAAE;UACnCkC,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,wBAAwB;UAC9BC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,IAAI,EAAE,CACJ,CAAC,cAAc,EAAE,CAACrF,aAAa,CAAC8D,WAAW,IAAI,CAAC,EAAEM,QAAQ,CAAC,CAAC,CAAC,EAC7D,CAAC,eAAe,EAAE,CAACpE,aAAa,CAACsF,YAAY,IAAI,CAAC,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAC/D,CAAC,aAAa,EAAE,CAACpE,aAAa,CAACuF,UAAU,IAAI,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAC3D,CAAC,mBAAmB,EAAE,CAACpE,aAAa,CAACwF,WAAW,IAAI,CAAC,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAClE,CAAC,sBAAsB,EAAE,IAAIvC,qBAAqB,CAAC,CAAC,EAAE,CAAC;UAE3D,CAAC;UACDoD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,uBAAuB;UAC7BC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACvB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,IAAI,EAAE,CACJ,CAAC,QAAQ,EAAE,eAAe,CAAC,EAC3B,GAAG7C,mBAAmB,CAAC,CAAC;UAE5B,CAAC;UACDyC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,mCAAmC;UACzCC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACvB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACpCC,IAAI,EAAE,CACJ,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,EAC3C,GAAGzB,oBAAoB,CAAC,CAAC;UAE7B,CAAC;UACDqB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,mCAAmC;UACzCC,KAAK,EAAE,eAAe;UACtBE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACvB,CAAC,EACD;UACEE,KAAK,EAAE;YACLC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC7BC,IAAI,EAAE,CACJ,CAAC,gBAAgB,EAAE,OAAO,EAAE,YAAY,CAAC,EACzC,GAAGf,sBAAsB,CAAC,CAAC;UAE/B,CAAC;UACDW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtB,CAAC;QAED;QACA;UACEH,IAAI,EAAE,gEAAgE;UACtEC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACtB,CAAC,CACF;QACDQ,MAAM,EAAE;UACNC,MAAM,EAAE;YACNC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDC,SAAS,EAAE;YACTH,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDE,aAAa,EAAE;YACbJ,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE;UACT,CAAC;UACDG,MAAM,EAAE;YACNL,QAAQ,EAAE,EAAE;YACZM,OAAO,EAAE,IAAI;YACbJ,KAAK,EAAE;UACT;QACF,CAAC;QACDK,YAAY,EAAE;UACZC,IAAI,EAAE,QAAQ;UACdC,aAAa,EAAE,CAAC,aAAa;QAC/B;MACF,CAAC;;MAED;MACAhI,OAAO,CAACiI,SAAS,CAACzB,aAAa,CAAC,CAAC0B,QAAQ,CAAC,kCAAkC,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,OAAO,CAAC,CAAC,MAAM,CAAC;MACvGlI,SAAS,CAACmI,OAAO,CAAC,mCAAmC,CAAC;IACxD,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD7B,SAAS,CAAC6B,KAAK,CAAC,8BAA8B,IAAIA,KAAK,CAACuG,OAAO,IAAI,eAAe,CAAC,CAAC;IACtF,CAAC,SAAS;MACR7F,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC1G,aAAa,EAAE;MAClB3B,SAAS,CAAC6B,KAAK,CAAC,8BAA8B,CAAC;MAC/C;IACF;IAEAY,qBAAqB,CAAC,IAAI,CAAC;IAE3B,IAAI;MACF,MAAMgC,WAAW,GAAG,IAAIrC,IAAI,CAAC,CAAC,CAACiE,kBAAkB,CAAC,OAAO,CAAC;;MAE1D;MACA,MAAMiC,QAAQ,GAAG,IAAIrI,OAAO,CAACsI,QAAQ,CAAC,CAAC;MACvC,MAAMC,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,kBAAkB,CAAC;;MAE3D;MACAD,SAAS,CAACE,OAAO,GAAG,CAClB;QAAEC,KAAK,EAAE;MAAG,CAAC;MAAE;MACf;QAAEA,KAAK,EAAE;MAAG,CAAC;MAAE;MACf;QAAEA,KAAK,EAAE;MAAG,CAAC,CAAE;MAAA,CAChB;MAED,IAAIC,UAAU,GAAG,CAAC;;MAElB;MACA,MAAMC,SAAS,GAAGL,SAAS,CAACM,OAAO,CAAC,IAAI,CAAC;MACzCD,SAAS,CAAC5E,KAAK,GAAG,uBAAuB;MACzC4E,SAAS,CAACf,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MACtEH,SAAS,CAACI,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MACrFH,SAAS,CAAClC,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAClER,SAAS,CAACS,MAAM,GAAG;QACjBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,OAAO,CAAC;MAE7B,MAAMC,YAAY,GAAGpB,SAAS,CAACM,OAAO,CAAC,IAAI,CAAC;MAC5Cc,YAAY,CAAC3F,KAAK,GAAG,gCAAgC;MACrD2F,YAAY,CAAC9B,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MACzEY,YAAY,CAACX,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MACxFY,YAAY,CAACjD,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACrEO,YAAY,CAACN,MAAM,GAAG;QACpBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,OAAO,CAAC;MAE7B,MAAME,QAAQ,GAAGrB,SAAS,CAACM,OAAO,CAAC,IAAI,CAAC;MACxCe,QAAQ,CAAC5F,KAAK,GAAG,gBAAgBQ,WAAW,EAAE;MAC9CoF,QAAQ,CAAC/B,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MACrEa,QAAQ,CAACZ,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MACpFa,QAAQ,CAAClD,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACjEQ,QAAQ,CAACP,MAAM,GAAG;QAChBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,OAAO,CAAC;MAE7Bf,UAAU,GAAG,CAAC;;MAEd;MACA,MAAMkB,kBAAkB,GAAGtB,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC9DkB,kBAAkB,CAAC7F,KAAK,GAAG,qBAAqB;MAChD6F,kBAAkB,CAAChC,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MAC/Ec,kBAAkB,CAACb,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC9Fc,kBAAkB,CAACnD,SAAS,GAAG;QAAEyC,UAAU,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACzES,kBAAkB,CAACR,MAAM,GAAG;QAC1BC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,IAAIf,UAAU,KAAKA,UAAU,EAAE,CAAC;MACrDA,UAAU,EAAE;;MAEZ;MACA,MAAMmB,gBAAgB,GAAGvB,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC5DmB,gBAAgB,CAAC9F,KAAK,GAAG,QAAQ;MACjC8F,gBAAgB,CAACjC,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAChDgB,gBAAgB,CAACd,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC5Fe,gBAAgB,CAACpD,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACzEU,gBAAgB,CAACT,MAAM,GAAG;QACxBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAMsD,eAAe,GAAGxB,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC3DoB,eAAe,CAAC/F,KAAK,GAAG,OAAO;MAC/B+F,eAAe,CAAClC,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAC/CiB,eAAe,CAACf,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC3FgB,eAAe,CAACrD,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACxEW,eAAe,CAACV,MAAM,GAAG;QACvBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACDkC,UAAU,EAAE;;MAEZ;MACA,MAAMqB,YAAY,GAAG,CACnB,CAAC,cAAc,EAAEtI,aAAa,CAAC8D,WAAW,IAAI,CAAC,CAAC,EAChD,CAAC,eAAe,EAAE9D,aAAa,CAACsF,YAAY,IAAI,CAAC,CAAC,EAClD,CAAC,aAAa,EAAEtF,aAAa,CAACuF,UAAU,IAAI,CAAC,CAAC,EAC9C,CAAC,cAAc,EAAEvF,aAAa,CAACwF,WAAW,IAAI,CAAC,CAAC,EAChD,CAAC,eAAe,EAAE,IAAI,CAACxF,aAAa,CAACmC,YAAY,IAAI,CAAC,EAAEI,cAAc,CAAC,CAAC,EAAE,CAAC,CAC5E;MAED+F,YAAY,CAACC,OAAO,CAAC,CAAC,CAACC,MAAM,EAAElG,KAAK,CAAC,KAAK;QACxC,MAAMmG,UAAU,GAAG5B,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;QACtDwB,UAAU,CAACnG,KAAK,GAAGkG,MAAM;QACzBC,UAAU,CAACtC,IAAI,GAAG;UAAEiB,IAAI,EAAE;QAAG,CAAC;QAC9BqB,UAAU,CAACzD,SAAS,GAAG;UAAEyC,UAAU,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAS,CAAC;QACjEe,UAAU,CAACd,MAAM,GAAG;UAClBC,GAAG,EAAE;YAAE7C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACnDQ,MAAM,EAAE;YAAE9C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACtDS,IAAI,EAAE;YAAE/C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACpDU,KAAK,EAAE;YAAEhD,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE;QACtD,CAAC;QAED,MAAMqB,SAAS,GAAG7B,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;QACrDyB,SAAS,CAACpG,KAAK,GAAGA,KAAK;QACvBoG,SAAS,CAACvC,IAAI,GAAG;UAAEiB,IAAI,EAAE;QAAG,CAAC;QAC7BsB,SAAS,CAAC1D,SAAS,GAAG;UAAEyC,UAAU,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAS,CAAC;QAChEgB,SAAS,CAACf,MAAM,GAAG;UACjBC,GAAG,EAAE;YAAE7C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACnDQ,MAAM,EAAE;YAAE9C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACtDS,IAAI,EAAE;YAAE/C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACpDU,KAAK,EAAE;YAAEhD,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE;QACtD,CAAC;QACDJ,UAAU,EAAE;MACd,CAAC,CAAC;MAEFA,UAAU,EAAE,CAAC,CAAC;;MAEd;MACA,MAAM0B,iBAAiB,GAAG9B,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC7D0B,iBAAiB,CAACrG,KAAK,GAAG,cAAc;MACxCqG,iBAAiB,CAACxC,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MAC9EsB,iBAAiB,CAACrB,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC7FsB,iBAAiB,CAAC3D,SAAS,GAAG;QAAEyC,UAAU,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACxEiB,iBAAiB,CAAChB,MAAM,GAAG;QACzBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,IAAIf,UAAU,KAAKA,UAAU,EAAE,CAAC;MACrDA,UAAU,EAAE;;MAEZ;MACA,MAAM2B,gBAAgB,GAAG/B,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC5D2B,gBAAgB,CAACtG,KAAK,GAAG,QAAQ;MACjCsG,gBAAgB,CAACzC,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAChDwB,gBAAgB,CAACtB,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC5FuB,gBAAgB,CAAC5D,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACzEkB,gBAAgB,CAACjB,MAAM,GAAG;QACxBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAM8D,sBAAsB,GAAGhC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAClE4B,sBAAsB,CAACvG,KAAK,GAAG,eAAe;MAC9CuG,sBAAsB,CAAC1C,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MACtDyB,sBAAsB,CAACvB,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAClGwB,sBAAsB,CAAC7D,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAC/EmB,sBAAsB,CAAClB,MAAM,GAAG;QAC9BC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACDkC,UAAU,EAAE;;MAEZ;MACA,IAAIjH,aAAa,CAACiC,WAAW,IAAIjC,aAAa,CAACiC,WAAW,CAACY,MAAM,EAAE;QACjE,MAAMC,WAAW,GAAG,IAAIrC,IAAI,CAAC,CAAC;QAC9B,MAAMsC,WAAW,GAAGD,WAAW,CAACpC,WAAW,CAAC,CAAC;QAC7C,MAAMsC,YAAY,GAAGF,WAAW,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC;QAE/CjD,aAAa,CAACiC,WAAW,CAACY,MAAM,CAAC0F,OAAO,CAAC,CAACpF,KAAK,EAAEC,KAAK,KAAK;UACzD;UACA,MAAMC,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;UACnC,IAAIwF,aAAa,GAAG,IAAI;UAExB,IAAIzF,UAAU,CAAC5B,MAAM,KAAK,CAAC,EAAE;YAC3B,MAAM8B,SAAS,GAAGC,QAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;YACzC,MAAMI,UAAU,GAAG,IAAIhD,IAAI,CAAC,GAAG4C,UAAU,CAAC,CAAC,CAAC,OAAOE,SAAS,EAAE,CAAC,CAACN,QAAQ,CAAC,CAAC,GAAG,CAAC;;YAE9E;YACA,IAAIM,SAAS,KAAKR,WAAW,IAAIU,UAAU,GAAGT,YAAY,EAAE;cAC1D8F,aAAa,GAAG,KAAK;YACvB;UACF;UAEA,IAAIA,aAAa,EAAE;YAAA,IAAAC,sBAAA;YACjB,MAAMC,MAAM,GAAG,EAAAD,sBAAA,GAAA/I,aAAa,CAACiC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,cAAA6G,sBAAA,uBAArCA,sBAAA,CAAuChJ,IAAI,CAACqD,KAAK,CAAC,KAAI,CAAC;YAEtE,MAAM6F,UAAU,GAAGpC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;YACtDgC,UAAU,CAAC3G,KAAK,GAAGa,KAAK;YACxB8F,UAAU,CAAC9C,IAAI,GAAG;cAAEiB,IAAI,EAAE;YAAG,CAAC;YAC9B6B,UAAU,CAACjE,SAAS,GAAG;cAAEyC,UAAU,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAS,CAAC;YACjEuB,UAAU,CAACtB,MAAM,GAAG;cAClBC,GAAG,EAAE;gBAAE7C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACnDQ,MAAM,EAAE;gBAAE9C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACtDS,IAAI,EAAE;gBAAE/C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACpDU,KAAK,EAAE;gBAAEhD,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE;YACtD,CAAC;YAED,MAAM6B,UAAU,GAAGrC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;YACtDiC,UAAU,CAAC5G,KAAK,GAAG,IAAI0G,MAAM,CAACzG,cAAc,CAAC,CAAC,EAAE;YAChD2G,UAAU,CAAC/C,IAAI,GAAG;cAAEiB,IAAI,EAAE;YAAG,CAAC;YAC9B8B,UAAU,CAAClE,SAAS,GAAG;cAAEyC,UAAU,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAS,CAAC;YACjEwB,UAAU,CAACvB,MAAM,GAAG;cAClBC,GAAG,EAAE;gBAAE7C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACnDQ,MAAM,EAAE;gBAAE9C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACtDS,IAAI,EAAE;gBAAE/C,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE,CAAC;cACpDU,KAAK,EAAE;gBAAEhD,KAAK,EAAE,MAAM;gBAAEc,KAAK,EAAE;kBAAEwB,IAAI,EAAE;gBAAW;cAAE;YACtD,CAAC;YACDJ,UAAU,EAAE;UACd;QACF,CAAC,CAAC;MACJ;MAEAA,UAAU,EAAE,CAAC,CAAC;;MAEd;MACA,MAAMkC,gBAAgB,GAAGtC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC5DkC,gBAAgB,CAAC7G,KAAK,GAAG,8BAA8B;MACvD6G,gBAAgB,CAAChD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MAC7E8B,gBAAgB,CAAC7B,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC5F8B,gBAAgB,CAACnE,SAAS,GAAG;QAAEyC,UAAU,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACvEyB,gBAAgB,CAACxB,MAAM,GAAG;QACxBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,IAAIf,UAAU,KAAKA,UAAU,EAAE,CAAC;MACrDA,UAAU,EAAE;;MAEZ;MACA,MAAMmC,eAAe,GAAGvC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC3DmC,eAAe,CAAC9G,KAAK,GAAG,QAAQ;MAChC8G,eAAe,CAACjD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAC/CgC,eAAe,CAAC9B,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC3F+B,eAAe,CAACpE,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACxE0B,eAAe,CAACzB,MAAM,GAAG;QACvBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAMsE,eAAe,GAAGxC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC3DoC,eAAe,CAAC/G,KAAK,GAAG,OAAO;MAC/B+G,eAAe,CAAClD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAC/CiC,eAAe,CAAC/B,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC3FgC,eAAe,CAACrE,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACxE2B,eAAe,CAAC1B,MAAM,GAAG;QACvBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAMuE,iBAAiB,GAAGzC,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC7DqC,iBAAiB,CAAChH,KAAK,GAAG,YAAY;MACtCgH,iBAAiB,CAACnD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MACjDkC,iBAAiB,CAAChC,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC7FiC,iBAAiB,CAACtE,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAC1E4B,iBAAiB,CAAC3B,MAAM,GAAG;QACzBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACDkC,UAAU,EAAE;;MAEZ;MACA,IAAIjH,aAAa,CAAC6D,iBAAiB,IAAI7D,aAAa,CAAC6D,iBAAiB,CAACpC,MAAM,GAAG,CAAC,EAAE;QACjF,MAAMqC,WAAW,GAAG9D,aAAa,CAAC6D,iBAAiB,CAACzB,MAAM,CAAC,CAACC,GAAG,EAAEsB,IAAI,KAAKtB,GAAG,IAAIsB,IAAI,CAACI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAErG/D,aAAa,CAAC6D,iBAAiB,CAAC0E,OAAO,CAAC5E,IAAI,IAAI;UAC9C,MAAMK,KAAK,GAAGL,IAAI,CAACI,KAAK,IAAI,CAAC;UAC7B,MAAME,UAAU,GAAGH,WAAW,GAAG,CAAC,GAAG,CAAEE,KAAK,GAAGF,WAAW,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAEnF,MAAMqF,UAAU,GAAG1C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACtDsC,UAAU,CAACjH,KAAK,GAAGqB,IAAI,CAACQ,MAAM,IAAI,SAAS;UAC3CoF,UAAU,CAACpD,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC9BmC,UAAU,CAACvE,SAAS,GAAG;YAAEyC,UAAU,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACjE6B,UAAU,CAAC5B,MAAM,GAAG;YAClBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UAED,MAAMmC,SAAS,GAAG3C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACrDuC,SAAS,CAAClH,KAAK,GAAG0B,KAAK;UACvBwF,SAAS,CAACrD,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC7BoC,SAAS,CAACxE,SAAS,GAAG;YAAEyC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UAClE8B,SAAS,CAAC7B,MAAM,GAAG;YACjBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UAED,MAAMoC,WAAW,GAAG5C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACvDwC,WAAW,CAACnH,KAAK,GAAG,GAAG2B,UAAU,GAAG;UACpCwF,WAAW,CAACtD,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC/BqC,WAAW,CAACzE,SAAS,GAAG;YAAEyC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACpE+B,WAAW,CAAC9B,MAAM,GAAG;YACnBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UACDJ,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMyC,UAAU,GAAG7C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;QACtDyC,UAAU,CAACpH,KAAK,GAAG,mBAAmB;QACtCoH,UAAU,CAACvD,IAAI,GAAG;UAAEiB,IAAI,EAAE;QAAG,CAAC;QAC9BsC,UAAU,CAAC1E,SAAS,GAAG;UAAEyC,UAAU,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAS,CAAC;QACjEgC,UAAU,CAAC/B,MAAM,GAAG;UAClBC,GAAG,EAAE;YAAE7C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACnDQ,MAAM,EAAE;YAAE9C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACtDS,IAAI,EAAE;YAAE/C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACpDU,KAAK,EAAE;YAAEhD,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE;QACtD,CAAC;QACDJ,UAAU,EAAE;MACd;MAEAA,UAAU,EAAE,CAAC,CAAC;;MAEd;MACA,MAAM0C,wBAAwB,GAAG9C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MACpE0C,wBAAwB,CAACrH,KAAK,GAAG,+BAA+B;MAChEqH,wBAAwB,CAACxD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE,EAAE;QAAEvB,KAAK,EAAE;UAAEwB,IAAI,EAAE;QAAW;MAAE,CAAC;MACrFsC,wBAAwB,CAACrC,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MACpGsC,wBAAwB,CAAC3E,SAAS,GAAG;QAAEyC,UAAU,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAC/EiC,wBAAwB,CAAChC,MAAM,GAAG;QAChCC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACD8B,SAAS,CAACmB,UAAU,CAAC,IAAIf,UAAU,KAAKA,UAAU,EAAE,CAAC;MACrDA,UAAU,EAAE;;MAEZ;MACA,MAAM2C,cAAc,GAAG/C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC1D2C,cAAc,CAACtH,KAAK,GAAG,gBAAgB;MACvCsH,cAAc,CAACzD,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAC9CwC,cAAc,CAACtC,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC1FuC,cAAc,CAAC5E,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACvEkC,cAAc,CAACjC,MAAM,GAAG;QACtBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAM8E,cAAc,GAAGhD,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC1D4C,cAAc,CAACvH,KAAK,GAAG,OAAO;MAC9BuH,cAAc,CAAC1D,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAC9CyC,cAAc,CAACvC,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC1FwC,cAAc,CAAC7E,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACvEmC,cAAc,CAAClC,MAAM,GAAG;QACtBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MAED,MAAM+E,gBAAgB,GAAGjD,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;MAC5D6C,gBAAgB,CAACxH,KAAK,GAAG,YAAY;MACrCwH,gBAAgB,CAAC3D,IAAI,GAAG;QAAEP,IAAI,EAAE,IAAI;QAAEwB,IAAI,EAAE;MAAG,CAAC;MAChD0C,gBAAgB,CAACxC,IAAI,GAAG;QAAEpG,IAAI,EAAE,SAAS;QAAEqG,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAW;MAAE,CAAC;MAC5FyC,gBAAgB,CAAC9E,SAAS,GAAG;QAAEyC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACzEoC,gBAAgB,CAACnC,MAAM,GAAG;QACxBC,GAAG,EAAE;UAAE7C,KAAK,EAAE;QAAO,CAAC;QAAE8C,MAAM,EAAE;UAAE9C,KAAK,EAAE;QAAO,CAAC;QACjD+C,IAAI,EAAE;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAAEgD,KAAK,EAAE;UAAEhD,KAAK,EAAE;QAAO;MAClD,CAAC;MACDkC,UAAU,EAAE;;MAEZ;MACA,IAAIjH,aAAa,CAACuE,iBAAiB,IAAIvE,aAAa,CAACuE,iBAAiB,CAAC9C,MAAM,GAAG,CAAC,EAAE;QACjF,MAAMqC,WAAW,GAAG9D,aAAa,CAACuE,iBAAiB,CAACnC,MAAM,CAAC,CAACC,GAAG,EAAEsB,IAAI,KAAKtB,GAAG,IAAIsB,IAAI,CAACI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAErG/D,aAAa,CAACuE,iBAAiB,CAACgE,OAAO,CAAC5E,IAAI,IAAI;UAC9C,MAAMK,KAAK,GAAGL,IAAI,CAACI,KAAK,IAAI,CAAC;UAC7B,MAAME,UAAU,GAAGH,WAAW,GAAG,CAAC,GAAG,CAAEE,KAAK,GAAGF,WAAW,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAEnF,MAAM6F,SAAS,GAAGlD,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACrD8C,SAAS,CAACzH,KAAK,GAAGqB,IAAI,CAACa,QAAQ,IAAI,SAAS;UAC5CuF,SAAS,CAAC5D,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC7B2C,SAAS,CAAC/E,SAAS,GAAG;YAAEyC,UAAU,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAS,CAAC;UAChEqC,SAAS,CAACpC,MAAM,GAAG;YACjBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UAED,MAAMmC,SAAS,GAAG3C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACrDuC,SAAS,CAAClH,KAAK,GAAG0B,KAAK;UACvBwF,SAAS,CAACrD,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC7BoC,SAAS,CAACxE,SAAS,GAAG;YAAEyC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UAClE8B,SAAS,CAAC7B,MAAM,GAAG;YACjBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UAED,MAAMoC,WAAW,GAAG5C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;UACvDwC,WAAW,CAACnH,KAAK,GAAG,GAAG2B,UAAU,GAAG;UACpCwF,WAAW,CAACtD,IAAI,GAAG;YAAEiB,IAAI,EAAE;UAAG,CAAC;UAC/BqC,WAAW,CAACzE,SAAS,GAAG;YAAEyC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACpE+B,WAAW,CAAC9B,MAAM,GAAG;YACnBC,GAAG,EAAE;cAAE7C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACnDQ,MAAM,EAAE;cAAE9C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACtDS,IAAI,EAAE;cAAE/C,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE,CAAC;YACpDU,KAAK,EAAE;cAAEhD,KAAK,EAAE,MAAM;cAAEc,KAAK,EAAE;gBAAEwB,IAAI,EAAE;cAAW;YAAE;UACtD,CAAC;UACDJ,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMyC,UAAU,GAAG7C,SAAS,CAACM,OAAO,CAAC,IAAIF,UAAU,EAAE,CAAC;QACtDyC,UAAU,CAACpH,KAAK,GAAG,mBAAmB;QACtCoH,UAAU,CAACvD,IAAI,GAAG;UAAEiB,IAAI,EAAE;QAAG,CAAC;QAC9BsC,UAAU,CAAC1E,SAAS,GAAG;UAAEyC,UAAU,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAS,CAAC;QACjEgC,UAAU,CAAC/B,MAAM,GAAG;UAClBC,GAAG,EAAE;YAAE7C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACnDQ,MAAM,EAAE;YAAE9C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACtDS,IAAI,EAAE;YAAE/C,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE,CAAC;UACpDU,KAAK,EAAE;YAAEhD,KAAK,EAAE,MAAM;YAAEc,KAAK,EAAE;cAAEwB,IAAI,EAAE;YAAW;UAAE;QACtD,CAAC;QACDJ,UAAU,EAAE;MACd;;MAEA;MACA,MAAM+C,MAAM,GAAG,MAAMrD,QAAQ,CAACsD,IAAI,CAACC,WAAW,CAAC,CAAC;MAChD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,MAAM,CAAC,EAAE;QAAE9I,IAAI,EAAE;MAAoE,CAAC,CAAC;MAC9G,MAAMmJ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;MACZG,CAAC,CAAClE,QAAQ,GAAG,kCAAkC,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,OAAO,CAAC,CAAC,OAAO;MAC1EiE,CAAC,CAACI,KAAK,CAAC,CAAC;MACTN,GAAG,CAACO,eAAe,CAACR,GAAG,CAAC;MAExBhM,SAAS,CAACmI,OAAO,CAAC,qCAAqC,CAAC;IAC1D,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD7B,SAAS,CAAC6B,KAAK,CAAC,gCAAgC,IAAIA,KAAK,CAACuG,OAAO,IAAI,eAAe,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3F,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMgK,eAAe,GAAGA,CAAC;IAAEC,IAAI;IAAEtE;EAAQ,CAAC,kBACxCvH,OAAA;IAAK8L,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChF/L,OAAA;MAAK8L,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/L,OAAA;QAAG8L,SAAS,EAAE,MAAMD,IAAI;MAAqB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDnM,OAAA;QAAA+L,QAAA,EAAIxE;MAAO;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAIA,OAAO,IAAI,OAAO,EAAE;MACtB,OAAO,CAACA,OAAO,GAAG,OAAO,EAAErH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7C,CAAC,MAAM,IAAIqH,OAAO,IAAI,IAAI,EAAE;MAC1B,OAAO,CAACA,OAAO,GAAG,IAAI,EAAErH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1C;IACA,OAAO,CAAAqH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhJ,cAAc,CAAC,CAAC,KAAI,GAAG;EACzC,CAAC;;EAED;EACA,IAAItC,OAAO,EAAE;IACX,oBACEf,OAAA;MAAK8L,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/L,OAAA;QAAK8L,SAAS,EAAC,kDAAkD;QAACjG,KAAK,EAAE;UAAEyG,MAAM,EAAE;QAAQ,CAAE;QAAAP,QAAA,eAC3F/L,OAAA;UAAK8L,SAAS,EAAC,6BAA6B;UAACS,IAAI,EAAC,QAAQ;UAAAR,QAAA,eACxD/L,OAAA;YAAM8L,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAInL,KAAK,EAAE;IACT,oBACEhB,OAAA;MAAK8L,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/L,OAAA;QAAK8L,SAAS,EAAC,oBAAoB;QAACS,IAAI,EAAC,OAAO;QAAAR,QAAA,gBAC9C/L,OAAA;UAAI8L,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzCnM,OAAA;UAAA+L,QAAA,EAAI/K;QAAK;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAKA,oBACEnM,OAAA;IAAK8L,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC/L,OAAA;MAAK8L,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/L,OAAA;QAAA+L,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BnM,OAAA;QAAK8L,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/L,OAAA;UACE8L,SAAS,EAAC,sBAAsB;UAChCU,OAAO,EAAEjH,kBAAmB;UAC5BkH,QAAQ,EAAEhL,aAAa,IAAIV,OAAQ;UAAAgL,QAAA,EAElCtK,aAAa,gBACZzB,OAAA,CAAAE,SAAA;YAAA6L,QAAA,gBACE/L,OAAA;cAAK8L,SAAS,EAAC,uCAAuC;cAACS,IAAI,EAAC,QAAQ;cAAAR,QAAA,eAClE/L,OAAA;gBAAM8L,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,0BAER;UAAA,eAAE,CAAC,gBAEHnM,OAAA,CAAAE,SAAA;YAAA6L,QAAA,gBACE/L,OAAA,CAAChB,SAAS;cAAC8M,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEhC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACTnM,OAAA;UACE8L,SAAS,EAAC,iBAAiB;UAC3BU,OAAO,EAAEhF,oBAAqB;UAC9BiF,QAAQ,EAAE9K,kBAAkB,IAAIZ,OAAQ;UAAAgL,QAAA,EAEvCpK,kBAAkB,gBACjB3B,OAAA,CAAAE,SAAA;YAAA6L,QAAA,gBACE/L,OAAA;cAAK8L,SAAS,EAAC,uCAAuC;cAACS,IAAI,EAAC,QAAQ;cAAAR,QAAA,eAClE/L,OAAA;gBAAM8L,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,0BAER;UAAA,eAAE,CAAC,gBAEHnM,OAAA,CAAAE,SAAA;YAAA6L,QAAA,gBACE/L,OAAA,CAACf,WAAW;cAAC6M,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAElC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnM,OAAA;MAAK8L,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/L,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,EAAKjL,aAAa,CAAC8D,WAAW,IAAI;UAAC;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCnM,OAAA;YAAA+L,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC/L,OAAA;YAAG8L,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnM,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,EAAKjL,aAAa,CAACsF,YAAY,IAAI;UAAC;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CnM,OAAA;YAAA+L,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC/L,OAAA;YAAG8L,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnM,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,EAAKjL,aAAa,CAACuF,UAAU,IAAI;UAAC;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxCnM,OAAA;YAAA+L,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvC/L,OAAA;YAAG8L,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnM,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,EAAKjL,aAAa,CAACwF,WAAW,IAAI;UAAC;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCnM,OAAA;YAAA+L,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC/L,OAAA;YAAG8L,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnM,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,EAAKjL,aAAa,CAAC4L,aAAa,IAAI;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CnM,OAAA;YAAA+L,QAAA,EAAG;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC/L,OAAA;YAAG8L,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnM,OAAA;QAAK8L,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/L,OAAA;YAAA+L,QAAA,GAAI,GAAC,EAACK,aAAa,CAACtL,aAAa,CAACmC,YAAY,IAAI,CAAC,CAAC;UAAA;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1DnM,OAAA;YAAA+L,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrC/L,OAAA;YAAG8L,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnM,OAAA;MAAK8L,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/L,OAAA;QAAK8L,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/L,OAAA;UAAA+L,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnM,OAAA;UAAK8L,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC/L,OAAA;YAAK8L,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/L,OAAA;cACE8L,SAAS,EAAE,cAAc3K,cAAc,KAAK,MAAM,GAAG,aAAa,GAAG,uBAAuB,EAAG;cAC/FqL,OAAO,EAAEA,CAAA,KAAM9J,kBAAkB,CAAC,MAAM,CAAE;cAAAqJ,QAAA,EAC3C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnM,OAAA;cACE8L,SAAS,EAAE,cAAc3K,cAAc,KAAK,OAAO,GAAG,aAAa,GAAG,uBAAuB,EAAG;cAChGqL,OAAO,EAAEA,CAAA,KAAM9J,kBAAkB,CAAC,OAAO,CAAE;cAAAqJ,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnM,OAAA;cACE8L,SAAS,EAAE,cAAc3K,cAAc,KAAK,MAAM,GAAG,aAAa,GAAG,uBAAuB,EAAG;cAC/FqL,OAAO,EAAEA,CAAA,KAAM9J,kBAAkB,CAAC,MAAM,CAAE;cAAAqJ,QAAA,EAC3C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLhL,cAAc,KAAK,OAAO,KAAIL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwB,cAAc,kBAC1DtC,OAAA;YACE8L,SAAS,EAAC,4BAA4B;YACtC1I,KAAK,EAAE/B,YAAa;YACpBsL,QAAQ,EAAGC,CAAC,IAAKtL,eAAe,CAACgD,QAAQ,CAACsI,CAAC,CAACC,MAAM,CAACzJ,KAAK,CAAC,CAAE;YAC3DyC,KAAK,EAAE;cAAEiH,QAAQ,EAAE;YAAQ,CAAE;YAAAf,QAAA,EAE5BjL,aAAa,CAACwB,cAAc,CAAC0B,GAAG,CAACjC,IAAI,iBACpC/B,OAAA;cAAmBoD,KAAK,EAAErB,IAAK;cAAAgK,QAAA,EAAEhK;YAAI,GAAxBA,IAAI;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6B,CAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnM,OAAA;QAAK8L,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB,EAAAzL,sBAAA,GAAAQ,aAAa,CAACiC,WAAW,cAAAzC,sBAAA,wBAAAC,uBAAA,GAAzBD,sBAAA,CAA2BqD,MAAM,cAAApD,uBAAA,uBAAjCA,uBAAA,CAAmCgC,MAAM,IAAG,CAAC,gBAC5CvC,OAAA,CAACrB,IAAI;UACHkC,IAAI,EAAEC,aAAa,CAACiC,WAAY;UAChCgK,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cACPC,MAAM,EAAE;gBACNC,QAAQ,EAAE;cACZ;YACF,CAAC;YACDC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,KAAK;gBAClBC,IAAI,EAAE;kBACJC,UAAU,EAAE;gBACd,CAAC;gBACDC,KAAK,EAAE;kBACLC,QAAQ,EAAGvK,KAAK,IAAK,GAAG,GAAGgJ,aAAa,CAAChJ,KAAK;gBAChD;cACF,CAAC;cACDwK,CAAC,EAAE;gBACDJ,IAAI,EAAE;kBACJK,OAAO,EAAE;gBACX;cACF;YACF;UACF;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFnM,OAAA,CAAC4L,eAAe;UAACC,IAAI,EAAC,aAAa;UAACtE,OAAO,EAAC;QAA2B;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC1E;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnM,OAAA;MAAK8L,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/L,OAAA;QAAK8L,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC/L,OAAA;UAAK8L,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B/L,OAAA;YAAA+L,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAAvL,qBAAA,GAAAM,aAAa,CAACgN,qBAAqB,cAAAtN,qBAAA,wBAAAC,sBAAA,GAAnCD,qBAAA,CAAqCmD,MAAM,cAAAlD,sBAAA,uBAA3CA,sBAAA,CAA6C8B,MAAM,IAAG,CAAC,gBACtDvC,OAAA,CAACpB,QAAQ;YACPiC,IAAI,EAAEC,aAAa,CAACgN,qBAAsB;YAC1Cf,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBzJ,MAAM,EAAE;oBACNoK,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMnN,IAAI,GAAGmN,KAAK,CAACnN,IAAI;sBACvB,IAAIA,IAAI,CAAC8C,MAAM,CAACpB,MAAM,IAAI1B,IAAI,CAACmC,QAAQ,CAACT,MAAM,EAAE;wBAC9C,MAAM0L,OAAO,GAAGpN,IAAI,CAACmC,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAM6B,KAAK,GAAGoJ,OAAO,CAACpN,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOvC,IAAI,CAAC8C,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEiK,CAAC,KAAK;0BAAA,IAAAC,oBAAA;0BACnC,MAAM/K,KAAK,GAAG6K,OAAO,CAACpN,IAAI,CAACqN,CAAC,CAAC;0BAC7B,MAAMnJ,UAAU,GAAG,CAAE3B,KAAK,GAAGyB,KAAK,GAAI,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACLY,IAAI,EAAE,GAAG3B,KAAK,KAAKb,KAAK,KAAK2B,UAAU,IAAI;4BAC3CqJ,SAAS,EAAEH,OAAO,CAACI,eAAe,CAACH,CAAC,CAAC;4BACrCI,WAAW,EAAE,EAAAH,oBAAA,GAAAF,OAAO,CAACM,WAAW,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAsBD,CAAC,CAAC,KAAI,MAAM;4BAC/CM,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbvK,KAAK,EAAEgK;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDQ,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACT1K,KAAK,EAAE,SAAAA,CAAS2K,OAAO,EAAE;sBACvB,MAAM3K,KAAK,GAAG2K,OAAO,CAAC3K,KAAK,IAAI,EAAE;sBACjC,MAAMb,KAAK,GAAGwL,OAAO,CAACC,MAAM;sBAC5B,MAAMhK,KAAK,GAAG+J,OAAO,CAACX,OAAO,CAACpN,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAE2L,GAAG,KAAK3L,GAAG,GAAG2L,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAM/J,UAAU,GAAG,CAAE3B,KAAK,GAAGyB,KAAK,GAAI,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAGf,KAAK,KAAKb,KAAK,eAAe2B,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDgK,MAAM,EAAE;YACV;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFnM,OAAA,CAAC4L,eAAe;YAACC,IAAI,EAAC,cAAc;YAACtE,OAAO,EAAC;UAAyB;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnM,OAAA;QAAK8L,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC/L,OAAA;UAAK8L,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B/L,OAAA;YAAA+L,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB,EAAArL,qBAAA,GAAAI,aAAa,CAACkO,iBAAiB,cAAAtO,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCiD,MAAM,cAAAhD,sBAAA,uBAAvCA,sBAAA,CAAyC4B,MAAM,IAAG,CAAC,gBAClDvC,OAAA,CAACiP,GAAG;YACFpO,IAAI,EAAEC,aAAa,CAACkO,iBAAkB;YACtCjC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,MAAM,EAAE;kBACNC,QAAQ,EAAE,QAAQ;kBAClBzJ,MAAM,EAAE;oBACNoK,cAAc,EAAE,SAAAA,CAASC,KAAK,EAAE;sBAC9B,MAAMnN,IAAI,GAAGmN,KAAK,CAACnN,IAAI;sBACvB,IAAIA,IAAI,CAAC8C,MAAM,CAACpB,MAAM,IAAI1B,IAAI,CAACmC,QAAQ,CAACT,MAAM,EAAE;wBAC9C,MAAM0L,OAAO,GAAGpN,IAAI,CAACmC,QAAQ,CAAC,CAAC,CAAC;wBAChC,MAAM6B,KAAK,GAAGoJ,OAAO,CAACpN,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;wBACjE,OAAOvC,IAAI,CAAC8C,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEiK,CAAC,KAAK;0BAAA,IAAAgB,qBAAA;0BACnC,MAAM9L,KAAK,GAAG6K,OAAO,CAACpN,IAAI,CAACqN,CAAC,CAAC;0BAC7B,MAAMnJ,UAAU,GAAG,CAAE3B,KAAK,GAAGyB,KAAK,GAAI,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;0BACrD,OAAO;4BACLY,IAAI,EAAE,GAAG3B,KAAK,KAAKb,KAAK,KAAK2B,UAAU,IAAI;4BAC3CqJ,SAAS,EAAEH,OAAO,CAACI,eAAe,CAACH,CAAC,CAAC;4BACrCI,WAAW,EAAE,EAAAY,qBAAA,GAAAjB,OAAO,CAACM,WAAW,cAAAW,qBAAA,uBAAnBA,qBAAA,CAAsBhB,CAAC,CAAC,KAAI,MAAM;4BAC/CM,SAAS,EAAE,CAAC;4BACZC,MAAM,EAAE,KAAK;4BACbvK,KAAK,EAAEgK;0BACT,CAAC;wBACH,CAAC,CAAC;sBACJ;sBACA,OAAO,EAAE;oBACX;kBACF;gBACF,CAAC;gBACDQ,OAAO,EAAE;kBACPC,SAAS,EAAE;oBACT1K,KAAK,EAAE,SAAAA,CAAS2K,OAAO,EAAE;sBACvB,MAAM3K,KAAK,GAAG2K,OAAO,CAAC3K,KAAK,IAAI,EAAE;sBACjC,MAAMb,KAAK,GAAGwL,OAAO,CAACC,MAAM;sBAC5B,MAAMhK,KAAK,GAAG+J,OAAO,CAACX,OAAO,CAACpN,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAE2L,GAAG,KAAK3L,GAAG,GAAG2L,GAAG,EAAE,CAAC,CAAC;sBACrE,MAAM/J,UAAU,GAAG,CAAE3B,KAAK,GAAGyB,KAAK,GAAI,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;sBACrD,OAAO,GAAGf,KAAK,KAAKb,KAAK,eAAe2B,UAAU,IAAI;oBACxD;kBACF;gBACF;cACF,CAAC;cACDoK,OAAO,EAAEA,CAACC,CAAC,EAAEC,cAAc,EAAErB,KAAK,KAAK;gBACrC,IAAIqB,cAAc,CAAC9M,MAAM,GAAG,CAAC,EAAE;kBAC7B,MAAM+M,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC,CAACnL,KAAK;kBACzC,MAAM+J,OAAO,GAAGD,KAAK,CAACnN,IAAI,CAACmC,QAAQ,CAAC,CAAC,CAAC;kBACtC,MAAM6B,KAAK,GAAGoJ,OAAO,CAACpN,IAAI,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAE2L,GAAG,KAAK3L,GAAG,GAAG2L,GAAG,EAAE,CAAC,CAAC;kBAC7D,MAAM1L,KAAK,GAAG6K,OAAO,CAACpN,IAAI,CAACyO,SAAS,CAAC;kBACrC,MAAMvK,UAAU,GAAG,CAAE3B,KAAK,GAAGyB,KAAK,GAAI,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;kBACrD,MAAMf,KAAK,GAAG+J,KAAK,CAACnN,IAAI,CAAC8C,MAAM,CAAC2L,SAAS,CAAC;;kBAE1C;kBACAtB,KAAK,CAACjB,OAAO,CAACG,OAAO,CAACqC,UAAU,GAAG;oBACjC1B,OAAO,EAAE,IAAI;oBACbjI,IAAI,EAAE,GAAGb,UAAU,GAAG;oBACtByK,OAAO,EAAEvL,KAAK;oBACdb,KAAK,EAAEA;kBACT,CAAC;kBACD4K,KAAK,CAACyB,MAAM,CAAC,MAAM,CAAC;gBACtB,CAAC,MAAM;kBACL;kBACAzB,KAAK,CAACjB,OAAO,CAACG,OAAO,CAACqC,UAAU,GAAG;oBACjC1B,OAAO,EAAE,IAAI;oBACbjI,IAAI,EAAE,OAAO;oBACb4J,OAAO,EAAE,iBAAiB;oBAC1BpM,KAAK,EAAE;kBACT,CAAC;kBACD4K,KAAK,CAACyB,MAAM,CAAC,MAAM,CAAC;gBACtB;cACF;YACF,CAAE;YACFvC,OAAO,EAAE,CAAC;cACRwC,EAAE,EAAE,eAAe;cACnBC,UAAU,EAAG3B,KAAK,IAAK;gBACrB,MAAM;kBAAE4B,GAAG;kBAAE9H,KAAK;kBAAEwE;gBAAO,CAAC,GAAG0B,KAAK;gBACpC,MAAMuB,UAAU,GAAGvB,KAAK,CAACjB,OAAO,CAACG,OAAO,CAACqC,UAAU,IAAI;kBACrD1B,OAAO,EAAE,IAAI;kBACbjI,IAAI,EAAE,OAAO;kBACb4J,OAAO,EAAE,iBAAiB;kBAC1BpM,KAAK,EAAE;gBACT,CAAC;gBAED,IAAImM,UAAU,CAAC1B,OAAO,EAAE;kBACtB+B,GAAG,CAACC,IAAI,CAAC,CAAC;kBACVD,GAAG,CAACE,SAAS,GAAG,QAAQ;kBACxBF,GAAG,CAACG,YAAY,GAAG,QAAQ;kBAE3B,MAAMC,OAAO,GAAGlI,KAAK,GAAG,CAAC;kBACzB,MAAMmI,OAAO,GAAG3D,MAAM,GAAG,CAAC;;kBAE1B;kBACAsD,GAAG,CAAC3I,IAAI,GAAG,iBAAiB;kBAC5B2I,GAAG,CAACxB,SAAS,GAAG,MAAM;kBACtBwB,GAAG,CAACM,QAAQ,CAACX,UAAU,CAAC3J,IAAI,EAAEoK,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC;;kBAEnD;kBACAL,GAAG,CAAC3I,IAAI,GAAG,YAAY;kBACvB2I,GAAG,CAACxB,SAAS,GAAG,MAAM;kBACtBwB,GAAG,CAACM,QAAQ,CAACX,UAAU,CAACC,OAAO,EAAEQ,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;;kBAEvD;kBACA,IAAIV,UAAU,CAACnM,KAAK,EAAE;oBACpBwM,GAAG,CAAC3I,IAAI,GAAG,YAAY;oBACvB2I,GAAG,CAACxB,SAAS,GAAG,MAAM;oBACtBwB,GAAG,CAACM,QAAQ,CAAC,GAAGX,UAAU,CAACnM,KAAK,YAAY,EAAE4M,OAAO,EAAEC,OAAO,GAAG,EAAE,CAAC;kBACtE;kBAEAL,GAAG,CAACO,OAAO,CAAC,CAAC;gBACf;cACF;YACF,CAAC;UAAE;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEFnM,OAAA,CAAC4L,eAAe;YAACC,IAAI,EAAC,mBAAmB;YAACtE,OAAO,EAAC;UAA2B;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAChF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnM,OAAA;MAAK8L,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC/L,OAAA;QAAK8L,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3C/L,OAAA;UAAK8L,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClG/L,OAAA;YAAI8L,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClB/L,OAAA;cAAG8L,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mDAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtC/L,OAAA;YAAK8L,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B/L,OAAA;cAAO8L,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC/L,OAAA;gBAAA+L,QAAA,eACE/L,OAAA;kBAAA+L,QAAA,gBACE/L,OAAA;oBAAA+L,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnM,OAAA;gBAAA+L,QAAA,EACG,CAACjL,aAAa,CAAC6D,iBAAiB,IAAI,EAAE,EAAEpC,MAAM,GAAG,CAAC,GACjD,CAACzB,aAAa,CAAC6D,iBAAiB,IAAI,EAAE,EAAEX,GAAG,CAAC,CAACoM,QAAQ,EAAElM,KAAK,kBAC1DlE,OAAA;kBAAA+L,QAAA,gBACE/L,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAA+L,QAAA,EAASqE,QAAQ,CAACnL;oBAAM;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEqE,QAAQ,CAACvL;oBAAK;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEqE,QAAQ,CAACjL;oBAAM;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEqE,QAAQ,CAACC;oBAAO;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAK8L,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC/L,OAAA;wBAAK8L,SAAS,EAAC,eAAe;wBAACjG,KAAK,EAAE;0BAAEiC,KAAK,EAAE,MAAM;0BAAEwE,MAAM,EAAE;wBAAM,CAAE;wBAAAP,QAAA,eACrE/L,OAAA;0BACE8L,SAAS,EAAC,yBAAyB;0BACnCjG,KAAK,EAAE;4BAAEiC,KAAK,EAAE,GAAGsI,QAAQ,CAACE,gBAAgB;0BAAI;wBAAE;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNnM,OAAA;wBAAA+L,QAAA,GAAQqE,QAAQ,CAACE,gBAAgB,EAAC,GAAC;sBAAA;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,EACGqE,QAAQ,CAACE,gBAAgB,IAAI,EAAE,gBAC9BtQ,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAC3CiE,QAAQ,CAACE,gBAAgB,IAAI,EAAE,gBACjCtQ,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDnM,OAAA;sBAAM8L,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAhCEjI,KAAK;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACL,CAAC,gBAEFnM,OAAA;kBAAA+L,QAAA,eACE/L,OAAA;oBAAIuQ,OAAO,EAAC,GAAG;oBAACzE,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAG8L,SAAS,EAAC;oBAA6B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,kEAEjD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnM,OAAA;QAAK8L,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3C/L,OAAA;UAAK8L,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAClG/L,OAAA;YAAI8L,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClB/L,OAAA;cAAG8L,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4DAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnM,OAAA;UAAK8L,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtC/L,OAAA;YAAK8L,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B/L,OAAA;cAAO8L,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC/L,OAAA;gBAAA+L,QAAA,eACE/L,OAAA;kBAAA+L,QAAA,gBACE/L,OAAA;oBAAA+L,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBnM,OAAA;oBAAA+L,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnM,OAAA;gBAAA+L,QAAA,EACG,CAACjL,aAAa,CAACuE,iBAAiB,IAAI,EAAE,EAAE9C,MAAM,GAAG,CAAC,GACjD,CAACzB,aAAa,CAACuE,iBAAiB,IAAI,EAAE,EAAErB,GAAG,CAAC,CAACsB,QAAQ,EAAEpB,KAAK,kBAC1DlE,OAAA;kBAAA+L,QAAA,gBACE/L,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAA+L,QAAA,EAASzG,QAAQ,CAACA;oBAAQ;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEzG,QAAQ,CAACT;oBAAK;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEzG,QAAQ,CAACH;oBAAM;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEzG,QAAQ,CAAC+K;oBAAO;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAK8L,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACvC,CAAC,GAAGyE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACxM,GAAG,CAAC,CAACoL,CAAC,EAAElB,CAAC,kBACtBlO,OAAA;wBAEE8L,SAAS,EAAE,MAAMoC,CAAC,GAAGuC,IAAI,CAACC,KAAK,CAACpL,QAAQ,CAACqL,SAAS,IAAI,CAAC,CAAC,GAAG,cAAc,GAAG,SAAS,oBAAqB;wBAC1G9K,KAAK,EAAE;0BAAEY,QAAQ,EAAE;wBAAO;sBAAE,GAFvByH,CAAC;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGJ,CACL,CAAC,eACFnM,OAAA;wBAAO8L,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,GAAC,EAACzG,QAAQ,CAACqL,SAAS,EAAC,GAAC;sBAAA;wBAAA3E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,eACE/L,OAAA;sBAAK8L,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC/L,OAAA;wBAAK8L,SAAS,EAAC,eAAe;wBAACjG,KAAK,EAAE;0BAAEiC,KAAK,EAAE,MAAM;0BAAEwE,MAAM,EAAE;wBAAM,CAAE;wBAAAP,QAAA,eACrE/L,OAAA;0BACE8L,SAAS,EAAC,yBAAyB;0BACnCjG,KAAK,EAAE;4BAAEiC,KAAK,EAAE,GAAGxC,QAAQ,CAACgL,gBAAgB;0BAAI;wBAAE;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNnM,OAAA;wBAAA+L,QAAA,GAAQzG,QAAQ,CAACgL,gBAAgB,EAAC,GAAC;sBAAA;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLnM,OAAA;oBAAA+L,QAAA,EACGzG,QAAQ,CAACqL,SAAS,IAAI,GAAG,gBACxB3Q,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAChD7G,QAAQ,CAACqL,SAAS,IAAI,GAAG,gBAC3B3Q,OAAA;sBAAM8L,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GACxC7G,QAAQ,CAACqL,SAAS,IAAI,GAAG,gBAC3B3Q,OAAA;sBAAM8L,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEpDnM,OAAA;sBAAM8L,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBACtD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA9CEjI,KAAK;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+CV,CACL,CAAC,gBAEFnM,OAAA;kBAAA+L,QAAA,eACE/L,OAAA;oBAAIuQ,OAAO,EAAC,GAAG;oBAACzE,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAG8L,SAAS,EAAC;oBAA8B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uEAElD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9L,EAAA,CA7xCID,aAAa;EAAA,QACAvB,WAAW,EACoBC,WAAW;AAAA;AAAA8R,EAAA,GAFvDxQ,aAAa;AA+xCnB,eAAeA,aAAa;AAAC,IAAAwQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}