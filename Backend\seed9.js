const mongoose = require("mongoose");
const Reservation = require("./src/models/reservation");
const Hotel = require("./src/models/hotel");
const Room = require("./src/models/room");
const User = require("./src/models/user");
require('dotenv').config();

// Kiểm tra ENVIRONMENT và chọn MongoDB URI phù hợp
const getMongoURI = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  console.log(`🌍 Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log(`📡 Using Production MongoDB: ${process.env.MONGODB_URI_PRODUCTION}`);
    return process.env.MONGODB_URI_PRODUCTION;
  } else {
    console.log(`💻 Using Development MongoDB: ${process.env.MONGODB_URI_DEVELOPMENT}`);
    return process.env.MONGODB_URI_DEVELOPMENT;
  }
};

const mongoURI = getMongoURI();
mongoose.connect(mongoURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log("✅ MongoDB connected successfully");
    console.log(`📍 Connected to: ${mongoURI.includes('mongodb+srv') ? 'MongoDB Atlas (Production)' : 'Local MongoDB (Development)'}`);
})
.catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
});

// Mock data cho 7 ngày gần đây (daily booking)
const dailyBookingData = [
  // 6 ngày trước
  { day: 6, bookings: 3, totalAmount: 800000 },
  { day: 6, bookings: 2, totalAmount: 600000 },
  { day: 6, bookings: 1, totalAmount: 400000 },
  
  // 5 ngày trước
  { day: 5, bookings: 4, totalAmount: 1200000 },
  { day: 5, bookings: 3, totalAmount: 900000 },
  { day: 5, bookings: 2, totalAmount: 500000 },
  { day: 5, bookings: 1, totalAmount: 300000 },
  
  // 4 ngày trước
  { day: 4, bookings: 2, totalAmount: 700000 },
  { day: 4, bookings: 2, totalAmount: 600000 },
  { day: 4, bookings: 1, totalAmount: 400000 },
  
  // 3 ngày trước
  { day: 3, bookings: 5, totalAmount: 1500000 },
  { day: 3, bookings: 3, totalAmount: 800000 },
  { day: 3, bookings: 2, totalAmount: 600000 },
  { day: 3, bookings: 1, totalAmount: 350000 },
  
  // 2 ngày trước
  { day: 2, bookings: 3, totalAmount: 900000 },
  { day: 2, bookings: 2, totalAmount: 700000 },
  { day: 2, bookings: 1, totalAmount: 450000 },
  
  // Hôm qua
  { day: 1, bookings: 4, totalAmount: 1100000 },
  { day: 1, bookings: 3, totalAmount: 850000 },
  { day: 1, bookings: 2, totalAmount: 600000 },
  { day: 1, bookings: 1, totalAmount: 400000 },
  
  // Hôm nay
  { day: 0, bookings: 2, totalAmount: 600000 },
  { day: 0, bookings: 1, totalAmount: 400000 },
  { day: 0, bookings: 1, totalAmount: 300000 }
];

// Hàm tạo ngày cho từng ngày
const getDateForDay = (daysAgo) => {
  const now = new Date();
  const targetDate = new Date(now);
  targetDate.setDate(now.getDate() - daysAgo);
  
  // Thêm random giờ trong ngày
  targetDate.setHours(Math.floor(Math.random() * 24));
  targetDate.setMinutes(Math.floor(Math.random() * 60));
  
  return targetDate;
};

// Hàm seed dữ liệu booking theo ngày
const seedDailyBookings = async () => {
  try {
    console.log("🏨 Đang lấy danh sách hotels và rooms...");
    
    // Lấy danh sách hotels và rooms
    const hotels = await Hotel.find({ adminStatus: "APPROVED" }).limit(10);
    const rooms = await Room.find({ statusActive: "ACTIVE" }).limit(20);
    const users = await User.find({ role: "CUSTOMER" }).limit(50);
    
    if (hotels.length === 0 || rooms.length === 0 || users.length === 0) {
      console.log("❌ Không tìm thấy đủ dữ liệu hotels, rooms hoặc users");
      return;
    }
    
    console.log(`✅ Tìm thấy ${hotels.length} hotels, ${rooms.length} rooms, ${users.length} users`);
    
    // Xóa dữ liệu booking cũ của 7 ngày gần đây
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    console.log("🗑️  Đang xóa dữ liệu booking cũ của 7 ngày gần đây...");
    const deleteResult = await Reservation.deleteMany({
      createdAt: { $gte: sevenDaysAgo }
    });
    console.log(`✅ Đã xóa ${deleteResult.deletedCount} booking cũ`);
    
    console.log("📝 Đang tạo mock data booking theo ngày...");
    
    const newBookings = [];
    let totalBookings = 0;
    let totalRevenue = 0;
    
    for (const data of dailyBookingData) {
      const bookingDate = getDateForDay(data.day);
      
      // Tạo booking với số tiền theo data
      const randomHotel = hotels[Math.floor(Math.random() * hotels.length)];
      const randomRoom = rooms[Math.floor(Math.random() * rooms.length)];
      const randomUser = users[Math.floor(Math.random() * users.length)];
      
      const checkInDate = new Date(bookingDate);
      checkInDate.setDate(checkInDate.getDate() + 1); // Check-in ngày hôm sau
      
      const checkOutDate = new Date(checkInDate);
      checkOutDate.setDate(checkInDate.getDate() + Math.floor(Math.random() * 3) + 1); // 1-3 đêm
      
      const booking = {
        user: randomUser._id,
        hotel: randomHotel._id,
        rooms: [{
          room: randomRoom._id,
          quantity: 1
        }],
        services: [],
        checkInDate,
        checkOutDate,
        status: "COMPLETED",
        totalPrice: data.totalAmount,
        finalPrice: data.totalAmount,
        createdAt: bookingDate,
        updatedAt: bookingDate
      };
      
      newBookings.push(booking);
      totalBookings++;
      totalRevenue += data.totalAmount;
    }
    
    // Insert tất cả booking
    const insertResult = await Reservation.insertMany(newBookings);
    console.log(`✅ Đã tạo ${insertResult.length} booking mới thành công`);
    
    // Thống kê theo ngày
    console.log("\n📊 Thống kê booking theo ngày:");
    const dayStats = {};
    
    dailyBookingData.forEach(data => {
      if (!dayStats[data.day]) {
        dayStats[data.day] = { bookings: 0, revenue: 0 };
      }
      dayStats[data.day].bookings += data.bookings;
      dayStats[data.day].revenue += data.totalAmount;
    });
    
    Object.keys(dayStats).forEach(day => {
      const stats = dayStats[day];
      const dayName = day == 0 ? 'Hôm nay' : day == 1 ? 'Hôm qua' : `${day} ngày trước`;
      console.log(`${dayName}: ${stats.bookings} booking, ${stats.revenue.toLocaleString('vi-VN')} VND`);
    });
    
    console.log(`\n💰 Tổng cộng: ${totalBookings} booking, ${totalRevenue.toLocaleString('vi-VN')} VND`);
    console.log("\n🎉 Seed dữ liệu booking theo ngày hoàn tất!");
    
    mongoose.disconnect();
  } catch (error) {
    console.error("❌ Lỗi khi seed dữ liệu:", error);
    mongoose.disconnect();
    process.exit(1);
  }
};

// Chạy seed function
seedDailyBookings();
